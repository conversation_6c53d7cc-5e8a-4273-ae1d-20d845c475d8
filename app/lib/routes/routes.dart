// ignore_for_file: constant_identifier_names

/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:01:08
 * @Description  : 路由的别名
 * 
 * @LastEditors: luoyuan <EMAIL>
 * @LastEditTime: 2022-05-05 12:24:47
 * @FilePath     : \flutter_metatel\lib\routes\routes.dart
 */
part of './pages.dart';

abstract class Routes {
  static const SPLASH = '/splash';
  static const HOME = '/home';
  static const LOGIN = '/login';
  static const MESSAGE = '/message';
  static const LOGIN_SMS = '/LoginSmsCode';
  static const SCAN = '/scan';
  static const ADDFRIEND = '/addFriend';
  static const SEARCH = '/search';
  static const CONTACT_DETAIL = '/contactDetail';
  static const WEBRTC_CALL = '/webrtcCall';
  static const ACCOUNT_MAIN = '/account_main';
  static const CREATE_GROUP = '/createGroup';
  static const PersonalInfo = '/personalInfo';
  static const PersonalInfoHead = '/personalInfoHead';
  static const MyQrcodePage = '/my_qr_code_page';
  static const AboutPage = '/about_page';
  static const NotifySetView = '/notify_set_view';
  static const MyCollectionView = '/my_collection_view';
  static const MnemonicVerifyPage = '/mnemonic_verify_page';
  static const CreateAccountInputRegisterCodePage = '/create_account_input_register_code_page';
  static const MnemonicPage = '/mnemonic_page';
  static const FirstMySelfInfoPage = '/first_my_self_info_page';
  static const SelectComNodeDialog = '/select_com_node_dialog';
  static const ChannelJoin = '/join';
  static const BrowserView = '/browserView';
  static const DAO = '/dao';
  static const PIN = '/pin';
  static const PinSetPage = '/pin_set';
  static const SearchDao = '/search_dao';

  static const DestroySetPage = '/destroy_set_page';
  static const DaoBrowserView = '/dao_browserView';

  static const WalletPwdModify = '/wallet_pwd_modify';
  static const WalletPwdReset = '/wallet_pwd_reset';
  static const PasswordView = '/password_view';
  static const MnemonicPageAfter = '/mnemonic_page_after';
  static const COMPLAINT = '/complaint';
  static const Blacklist = '/blacklist';
  static const InvitePage = '/invitePage';
  static const MiningPage = '/miningPage';
  static const My3TDetailPage = '/my3TDetailPage';
  static const OfficialDetailPage = '/officialDetailPage';
  static const SearchSquare = '/search_square';
  static const SplashLanguagePage = '/splash_language_page';
  static const ChannelBlackListPage = '/channel_black_list_page';
  static const DidListPage = '/did_list_page';
  static const NodeSwitchingPage = '/node_swiching_page';
  static const NodeStakingListView = '/node_staking_list_view';
  static const NodePledgeApplicationView = '/node_pledge_application_view_1';
  static const MnemonicBackupView = '/mnemonic_backup_view';
  static const ChannelMomentView = '/channel_moment_view';
  static const CreatePostView = '/create_post_view';
  static const MomentDetailView = '/moment_detail_view';
  static const MomentShareView = '/moment_share_view';
  static const MomentDescEditView = '/moment_desc_edit_view';
  static const MeetingView = '/meeting_view';
  static const CreateEditMeetingView = '/create_edit_meeting_view';
  static const PhonePage = '/PhonePage';
  static const AgreementView = '/agreement_view';
  static const InviteDetailPage = '/invite_detail_page';
  static const GameCenterMemberView = '/game_center/member_view';
}
