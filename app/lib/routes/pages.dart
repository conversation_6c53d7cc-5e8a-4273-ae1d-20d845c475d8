/*
 * <AUTHOR> <PERSON><PERSON>
 * @Date         : 2022-04-21 10:00:33
 * @Description  : 全局页面路由相关
 * 
 * @LastEditors  : <PERSON><PERSON>
 * @LastEditTime : 2022-05-07 16:22:38
 * @FilePath     : /flutter_metatel/lib/routes/pages.dart
 */

import 'package:flutter_metatel/app/modules/account/mnemonic/backup/mnemonic_backup_binding.dart';
import 'package:flutter_metatel/app/modules/account/mnemonic/backup/mnemonic_backup_view.dart';
import 'package:flutter_metatel/app/modules/account/mnemonic/phone/phone_page.dart';
import 'package:flutter_metatel/app/modules/account/mnemonic/resgiscode/create_account_input_reg_code_view.dart';
import 'package:flutter_metatel/app/modules/account/mnemonic/verify/mnemonic_verify_binding.dart';
import 'package:flutter_metatel/app/modules/agreement/agreement_view.dart';
import 'package:flutter_metatel/app/modules/browser/browser_binding.dart';
import 'package:flutter_metatel/app/modules/browser/browser_view.dart';
import 'package:flutter_metatel/app/modules/game_center/member/member_binding.dart';
import 'package:flutter_metatel/app/modules/game_center/member/member_view.dart';
import 'package:flutter_metatel/app/modules/meeting/meeting_binding.dart';
import 'package:flutter_metatel/app/modules/meeting/meeting_view.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/channel_moment_view.dart';
import 'package:flutter_metatel/app/modules/group/create/create_group_page.dart';
import 'package:flutter_metatel/app/modules/group/other/channel_black_list_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/menu/addFriend/add_friend_binding.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/menu/addFriend/add_friend_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>';
import 'package:flutter_metatel/app/modules/home/<USER>/blacklist/blacklist_binding.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/blacklist/blacklist_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/invite/invite_page_oversea.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/destory/destroy_set_page.dart';
import 'package:flutter_metatel/app/modules/home/<USER>/pin/pin_set_page.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/channel_moment_view.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/create_post_view.dart';
import 'package:flutter_metatel/app/modules/message/channelMoment/moment_share_view.dart';
import 'package:flutter_metatel/app/modules/official/official_detail_page.dart';
import 'package:flutter_metatel/app/modules/splash/splash_binging.dart';
import 'package:flutter_metatel/app/modules/splash/splash_language_page.dart';
import 'package:flutter_metatel/app/modules/square/search/search_square_binding.dart';
import 'package:flutter_metatel/app/modules/square/search/search_square_view.dart';
import 'package:get/get.dart';

import '../app/modules/account/main/account_main_view_im.dart';
import '../app/modules/account/mnemonic/verify/mnemonic_verify_view.dart';
import '../app/modules/account/mnemonic/view/mnemonic_view.dart';
import '../app/modules/account/mnemonic/view/mnemonic_view_after.dart';
import '../app/modules/dao/daoBrowser/dao_browser_binding.dart';
import '../app/modules/dao/daoBrowser/dao_browser_view.dart';
import '../app/modules/dao/dao_view.dart';
import '../app/modules/dao/search/search_dao_binding.dart';
import '../app/modules/dao/search/search_dao_view.dart';
import '../app/modules/did/did_list_binding.dart';
import '../app/modules/did/did_list_view.dart';
import '../app/modules/group/create/create_group_binding.dart';
import '../app/modules/group/join/join_binding.dart';
import '../app/modules/group/join/join_view.dart';
import '../app/modules/home/<USER>/menu/scan/scan_binding.dart';
import '../app/modules/home/<USER>/menu/scan/scan_page.dart';
import '../app/modules/home/<USER>/detail/contact_detail.dart';
import '../app/modules/home/<USER>';
import '../app/modules/home/<USER>/invite/invite_detail_binding.dart';
import '../app/modules/home/<USER>/invite/invite_detail_page.dart';
import '../app/modules/home/<USER>/myqrcode/my_qrcode_page.dart';
import '../app/modules/home/<USER>/node/node_switching_page.dart';
import '../app/modules/home/<USER>/notifyset/notify_set_page.dart';
import '../app/modules/home/<USER>/setmyselfinfo/first/first_my_self_info.dart';
import '../app/modules/message/channelMoment/moment_desc_edit_binding.dart';
import '../app/modules/message/channelMoment/moment_desc_edit_view.dart';
import '../app/modules/meeting/create_edit_meeting/create_edit_meeting_binding.dart';
import '../app/modules/meeting/create_edit_meeting/create_edit_meeting_view.dart';
import '../app/modules/message/channelMoment/moment_detail_view.dart';
import '../app/modules/message/message_page.dart';
import '../app/modules/password/pin/password_view.dart';
import '../app/modules/search/search_binding.dart';
import '../app/modules/search/search_view.dart';
import '../app/modules/splash/splash_page.dart';
import '../app/modules/wallet/pwd/reset/wallet_pwd_reset.dart';
import '../app/modules/webrtc/webrtc_view.dart';

part './routes.dart';

abstract class AppPages {
  static final pages = [
    GetPage(
      name: Routes.SPLASH,
      page: () => const SplashPage(),
      binding: SplashBinding(),
    ),
    GetPage(
      name: Routes.HOME,
      page: () => HomePage(
        key: homePageState,
      ),
      binding: HomeBinding(),
    ),
    // GetPage(
    //   name: Routes.LOGIN,
    //   page: () => const LoginView(),
    //   binding: LoginBinding(),
    // ),
    GetPage(
      name: Routes.MESSAGE,
      page: () => const MessagePage(),
      /*binding: MessageBinding(),*/
    ),
    // GetPage(
    //   name: Routes.LOGIN_SMS,
    //   page: () => const LoginSmsCodeView(),
    // ),
    GetPage(
      name: Routes.SCAN,
      page: () => const ScanPage(),
      binding: ScanBinding(),
    ),
    GetPage(
      name: Routes.ADDFRIEND,
      page: () => AddFriendPage(),
      binding: AddFriendBinding(),
    ),
    GetPage(
      name: Routes.SEARCH,
      page: () => SearchView(),
      binding: SearchBinding(),
    ),
    GetPage(
      name: Routes.CONTACT_DETAIL,
      page: () => const ContactDetailView(),
    ),
    GetPage(
      name: Routes.CREATE_GROUP,
      page: () => const CreateGroupPage(),
      binding: CreateGroupBinding(),
    ),
    GetPage(
      name: Routes.WEBRTC_CALL,
      page: () => const WebRtcView(),
    ),
    GetPage(
      name: Routes.ACCOUNT_MAIN,
      page: () {
        return const AccountMainImPage();
      },
    ),
    // GetPage(
    //   name: Routes.PersonalInfo,
    //   page: () => PersonalInfoView(),
    // ),
    // GetPage(
    //   name: Routes.PersonalInfoHead,
    //   page: () => PersonalInfoHeadView(),
    // ),
    GetPage(
      name: Routes.MyQrcodePage,
      page: () => MyQrcodePage(),
    ),

    GetPage(
      name: Routes.NotifySetView,
      page: () => NotifySetView(),
    ),
    GetPage(
      name: Routes.MnemonicVerifyPage,
      page: () => const MnemonicVerifyPage(),
      binding: MnemonicVerifyBinding(),
    ),
    GetPage(
      name: Routes.CreateAccountInputRegisterCodePage,
      page: () => const CreateAccountInputRegisterCodePage(),
    ),
    GetPage(
      name: Routes.MnemonicPage,
      page: () => const MnemonicPage(),
    ),
    GetPage(
      name: Routes.FirstMySelfInfoPage,
      page: () => const FirstMySelfInfoPage(),
    ),
    GetPage(
      name: Routes.MnemonicPageAfter,
      page: () => const MnemonicPageAfter(),
    ),
    GetPage(
      name: Routes.ChannelJoin,
      page: () => const JoinView(),
      binding: JoinBinding(),
    ),
    GetPage(
      name: Routes.BrowserView,
      page: () => BrowserView(),
      binding: BrowserBinding(),
    ),
    GetPage(
      name: Routes.DAO,
      page: () => const DaoView(),
    ),
    GetPage(
      name: Routes.PIN,
      page: () => const PinSetView(),
    ),
    GetPage(
      name: Routes.DestroySetPage,
      page: () => const DestroySetView(),
    ),
    GetPage(
      name: Routes.SearchDao,
      page: () => SearchDaoView(),
      binding: SearchDaoBinding(),
    ),
    GetPage(
      name: Routes.DaoBrowserView,
      page: () => DaoBrowserView(),
      binding: DaoBrowserBinding(),
    ),
    // GetPage(
    //   name: Routes.WalletPwdModify,
    //   page: () => const WalletPwdModifyPage(),
    // ),
    GetPage(
      name: Routes.WalletPwdReset,
      page: () => const WalletPwdResetPage(),
    ),
    GetPage(
      name: Routes.PasswordView,
      page: () => const PasswordView(),
    ),
    GetPage(
      name: Routes.Blacklist,
      page: () => const BlacklistPage(),
      binding: BlacklistBinding(),
    ),
    GetPage(
      name: Routes.InvitePage,
      page: () => const InvitePageOversea(),
    ),

    GetPage(
      name: Routes.OfficialDetailPage,
      page: () => const OfficialDetailPage(),
    ),
    GetPage(
      name: Routes.SearchSquare,
      page: () => SearchSquareView(),
      binding: SearchSquareBinding(),
    ),
    GetPage(
      name: Routes.SplashLanguagePage,
      page: () => const SplashLanguagePage(),
    ),
    GetPage(
      name: Routes.ChannelBlackListPage,
      page: () => const ChannelBlackListPage(),
      /*binding: MessageBinding(),*/
    ),
    GetPage(
      name: Routes.DidListPage,
      page: () => const DidListView(),
      binding: DidListBinding(),
    ),
    GetPage(
      name: Routes.NodeSwitchingPage,
      page: () => NodeSwitchingView(),
    ),

    GetPage(name: Routes.MnemonicBackupView, page: () => const MnemonicBackupView(), binding: MnemonicBackupBinding()),
    GetPage(
      name: Routes.ChannelMomentView,
      page: () => const ChannelMomentView(),
    ),
    GetPage(
      name: Routes.CreatePostView,
      page: () => const CreatePostView(),
      transition: Transition.downToUp,
    ),
    GetPage(
      name: Routes.MomentDetailView,
      page: () => const MomentDetailView(),
    ),
    GetPage(
      name: Routes.MomentShareView,
      page: () => const MomentShareView(),
    ),
    GetPage(name: Routes.MomentDescEditView, page: () => const MomentDescEditView(), binding: MomentDescEditBinding()),
    GetPage(
      name: Routes.PhonePage,
      page: () => const PhonePage(),
    ),
    GetPage(
      name: Routes.MeetingView,
      page: () => MeetingView(),
      binding: MeetingBinding(),
    ),
    GetPage(
      name: Routes.CreateEditMeetingView,
      page: () => CreateEditMeetingView(),
      binding: CreateEditMeetingBinding(),
    ),
    GetPage(
      name: Routes.AgreementView,
      page: () => AgreementView(),
      // binding: ,
    ),
    GetPage(
      name: Routes.InviteDetailPage,
      page: () => InviteDetailPage(),
      binding: InviteDetailBinding(),
    ),
    GetPage(
      name: Routes.GameCenterMemberView,
      page: () => MemberView(),
      binding: MemberBinding(),
    ),
  ];
}
