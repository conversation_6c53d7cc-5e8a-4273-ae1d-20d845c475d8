// DO NOT EDIT. This is code generated via package:get_cli/get_cli.dart

// ignore_for_file: lines_longer_than_80_chars, constant_identifier_names
// ignore: avoid_classes_with_only_static_members
import 'package:get/get.dart';

import '../values/config.dart';
import 'l_hw.dart';

class AppTranslation extends Translations {
  @override
  Map<String, Map<String, String>> keys = {
    'en_US': Locales.getLocales("en_US"),
    'zh_CN': Locales.getLocales("zh_CN"),
  };
}

class L {
  L._();

  static const close = 'close';
  static const download_now = 'download_now';
  static const youyuan_desc = 'youyuan_desc';

  static const youyuan_uniapp = 'youyuan_uniapp';
  static const certification_in_progress = 'certification_in_progress';
  static const certification_in_end = 'certification_in_end';
  static const today_remaining_times = 'today_remaining_times';
  static const today_number_of_times_hasbeen_used_up_pleasetry_again_tomorrow =
      'today_number_of_times_hasbeen_used_up_pleasetry_again_tomorrow';
  static const unverified = 'unverified';
  static const verified = 'verified';

  static const phone = 'phone';
  static const build_meaningful_connections_with_like_minded_people =
      "build_meaningful_connections_with_like_minded_people";
  static const join_thecommunity_and_co_create_brilliance = 'join_thecommunity_and_co_create_brilliance';
  static const real_authenticate_success = 'real_authenticate_success';
  static const start_certification = 'start_certification';
  static const real_authenticate_error = 'real_authenticate_error';

  static const you_have_not_yet_verified_your_name = 'you_have_not_yet_verified_your_name';

  static const start_real_name_authentication = 'start_real_name_authentication';
  static const want_to_unlock_more_exciting_things = 'want_to_unlock_more_exciting_things';
  static const uniapp_not_installed = 'uniapp_not_installed';
  static const retry = 'retry';
  static const no_real_name_authentication = 'no_real_name_authentication';
  static const add_some_friends = 'add_some_friends';
  static const share_your_invitation_code_to_invite_more_friends_to_join =
      'share_your_invitation_code_to_invite_more_friends_to_join';

  static const please_click_to_re_your_card_wallet = 'please_click_to_re_your_card_wallet';

  static const sensing_timeout = 'sensing_timeout';
  static const scan_successfully_and_confirm_with_fingerprint_or_pin_code =
      'scan_successfully_and_confirm_with_fingerprint_or_pin_code';
  static const please_bring_your_card_wallet_closer_for_scanning = 'please_bring_your_card_wallet_closer_for_scanning';
  static const ready_to_scan = 'ready_to_scan';
  static const note_pin_error = 'note_pin_error';
  static const your_card_wallet_has_failed_left = 'your_card_wallet_has_failed_left';
  static const your_card_wallet_has_failed_midl = 'your_card_wallet_has_failed_midl';
  static const your_card_wallet_has_failed_right = 'your_card_wallet_has_failed_right';
  static const your_card_wallet_pin_verification_has_been_locked = 'your_card_wallet_pin_verification_has_been_locked';
  static const the_wallet_address_does_not_match_please_select_the_correct_wallet =
      'the_wallet_address_does_not_match_please_select_the_correct_wallet';
  static const please_confirm_the_fingerprint_signature_on_the_hardware_card_wallet =
      'please_confirm_the_fingerprint_signature_on_the_hardware_card_wallet';
  static const please_use_fingerprint_to_pay = 'please_use_fingerprint_to_pay';
  static const fingerprint_payment = 'fingerprint_payment';
  static const pin_code_payment = 'pin_code_payment';
  static const transaction_to_create_miner_failed = 'transaction_to_create_miner_failed';
  static const exceeded_the_aximum_number_of_sends = 'exceeded_the_aximum_number_of_sends';
  static const exit_meeting = 'exit_meeting';
  static const select_gas_fee = 'select_gas_fee';
  static const recommendation = 'recommendation';
  static const recommendation_value = 'recommendation_value';
  static const gas_fee_detail_info = 'gas_fee_detail_info';
  static const customizable = 'customizable';
  static const set_as_default = 'set_as_default';
  static const set_as_default_dailog_info = 'set_as_default_dailog_info';

  static const gas_price = 'gas_price';
  static const gas_limit = 'gas_limit';
  static const fee = 'fee';

  static const speedy = 'speedy';
  static const speedy_time = 'speedy_time';
  static const custom = 'custom';

  static const what_is_the_red_packet_for = 'what_is_the_red_packet_for';
  static const loading_the_keystore = 'loading_the_keystore';
  static const create_a_bls_account = 'create_a_bls_account';
  static const create_absenteeism = 'create_absenteeism';
  static const start_the_blockchain_node = 'start_the_blockchain_node';
  static const banned = 'banned';
  static const reviewing = 'reviewing';
  static const status = 'status';
  static const also_clear_pinned_messages = 'also_clear_pinned_messages';
  static const randomly_generated = 'randomly_generated';
  static const state_county_province_or_region = 'state_county_province_or_region';
  static const postal_code = 'postal_code';
  static const city = 'city';
  static const address_line1 = 'address_line1';
  static const address_line2 = 'address_line2';
  static const country = 'country';
  static const billing_address = 'billing_address';
  static const name_on_card = 'name_on_card';
  static const please_enter_your_name = 'please_enter_your_name';
  static const add_card_information = 'add_card_information';
  static const transfer_out_et_hint = 'transfer_out_et_hint';
  static const transfer_out_hint = 'transfer_out_hint';
  static const transfer_out = 'transfer_out';
  static const delete_card = 'delete_card';
  static const delete_card_hint = 'delete_card_hint';
  static const quota = 'quota';
  static const time = 'time';
  static const virtual_card = 'virtual_card';
  static const transaction_record = 'transaction_record';
  static const base_account = 'base_account';
  static const account_name = 'account_name';
  static const freeze = 'freeze';
  static const normal = 'normal';
  static const card_list = 'card_list';
  static const recharge = 'recharge';
  static const app_name = 'app_name';
  static const registButton = 'registButton';
  static const qrcode = 'qrcode';
  static const my_qrcode = 'my_qrcode';
  static const scaner_hint = 'scaner_hint';
  static const qrcode_edit = 'qrcode_edit';
  static const choose_number = 'choose_number';
  static const vitualnumber_regist = 'vitualnumber_regist';
  static const phone_regist = 'phone_regist';
  static const hint_phone = 'hint_phone';
  static const hint_verifycode = 'hint_verifycode';
  static const get_verifycode = 'get_verifycode';
  static const second = 'second';
  static const regist_submit = 'regist_submit';
  static const bad_network = 'bad_network';
  static const metatel_file_assistant = 'metatel_file_assistant';
  static const metatel_server_number = 'metatel_server_number';
  static const metatel_system_warning = 'metatel_system_warning';
  static const module_call_busy = 'module_call_busy';
  static const channel_name_of_application_notification = 'channel_name_of_application_notification';
  static const channel_description_of_application_notification = 'channel_description_of_application_notification';
  static const channel_name_of_message_notification = 'channel_name_of_message_notification';
  static const channel_description_of_message_notification = 'channel_description_of_message_notification';
  static const foreground_service_started = 'foreground_service_started';
  static const module_activity_web_url_failure_toast_text = 'module_activity_web_url_failure_toast_text';
  static const module_activity_theme_details_tv_apply_text = 'module_activity_theme_details_tv_apply_text';
  static const module_activity_theme_set_success_text = 'module_activity_theme_set_success_text';
  static const module_activity_edit_qrcode_lv_item_tv_areacode_text =
      'module_activity_edit_qrcode_lv_item_tv_areacode_text';
  static const module_activity_edit_qrcode_lv_item_tv_horizontal_line_text =
      'module_activity_edit_qrcode_lv_item_tv_horizontal_line_text';
  static const module_activity_edit_qrcode_lv_item_tv_metatelnum_text =
      'module_activity_edit_qrcode_lv_item_tv_metatelnum_text';
  static const module_activity_edit_qrcode_lv_item_tv_title_text = 'module_activity_edit_qrcode_lv_item_tv_title_text';
  static const module_activity_edit_qrcode_lv_item_tv_name_hint_text =
      'module_activity_edit_qrcode_lv_item_tv_name_hint_text';
  static const module_activity_edit_qrcode_lv_item_tv_organization_hint_text =
      'module_activity_edit_qrcode_lv_item_tv_organization_hint_text';
  static const module_activity_edit_qrcode_lv_item_tv_save_text = 'module_activity_edit_qrcode_lv_item_tv_save_text';
  static const module_activity_group_qrcode_tv_lockinvite_hint_text =
      'module_activity_group_qrcode_tv_lockinvite_hint_text';
  static const module_activity_boot_set_home_title_text = 'module_activity_boot_set_home_title_text';
  static const module_activity_boot_set_sigle_permiss_title_text = 'module_activity_boot_set_sigle_permiss_title_text';
  static const module_activity_boot_set_high_power_title_text = 'module_activity_boot_set_high_power_title_text';
  static const module_activity_boot_set_boot_manager_text = 'module_activity_boot_set_boot_manager_text';
  static const module_activity_boot_set_stay_connect_text = 'module_activity_boot_set_stay_connect_text';
  static const module_activity_boot_set_process_manager_text = 'module_activity_boot_set_process_manager_text';
  static const module_activity_boot_set_limit_system_text = 'module_activity_boot_set_limit_system_text';
  static const module_activity_boot_set_ignore_battery_text = 'module_activity_boot_set_ignore_battery_text';
  static const module_activity_boot_set_notify_manager_text = 'module_activity_boot_set_notify_manager_text';
  static const module_activity_boot_set_open_notify_title_text = 'module_activity_boot_set_open_notify_title_text';
  static const module_activity_boot_set_confirm_text = 'module_activity_boot_set_confirm_text';
  static const module_activity_boot_set_next_step_text = 'module_activity_boot_set_next_step_text';
  static const module_activity_boot_set_ignore_battery_toast_text =
      'module_activity_boot_set_ignore_battery_toast_text';
  static const module_activity_boot_set_notify_has_set_toast_text =
      'module_activity_boot_set_notify_has_set_toast_text';
  static const module_activity_system_setting_button_text = 'module_activity_system_setting_button_text';
  static const module_fragment_contactor_title_friend_request_text =
      'module_fragment_contactor_title_friend_request_text';
  static const module_fragment_contactor_title_my_grou_text = 'module_fragment_contactor_title_my_grou_text';
  static const module_fragment_contactor_title_company_list_text = 'module_fragment_contactor_title_company_list_text';
  static const module_fragment_contactor_title_my_friend_text = 'module_fragment_contactor_title_my_friend_text';
  static const module_activity_chat_has_expired_text = 'module_activity_chat_has_expired_text';
  static const module_activity_chat_toast_none_wps_text = 'module_activity_chat_toast_none_wps_text';
  static const audio_meeting_create_load = 'audio_meeting_create_load';
  static const audio_meeting_accept_load = 'audio_meeting_accept_load';
  static const audio_meeting_again_in_load = 'audio_meeting_again_in_load';
  static const module_fragment_dial_call_failure_toast_text = 'module_fragment_dial_call_failure_toast_text';
  static const load_one = 'load_one';
  static const searbar_hint_search = 'searbar_hint_search';
  static const module_activity_group_chat_tv_text_no_data = 'module_activity_group_chat_tv_text_no_data';
  static const module_activity_search_tv_text_please_input = 'module_activity_search_tv_text_please_input';
  static const module_activity_encrypt_file_tv_title_text = 'module_activity_encrypt_file_tv_title_text';
  static const module_activity_encrypt_file_tv_no_such_file_text = 'module_activity_encrypt_file_tv_no_such_file_text';
  static const module_activity_audioplayer_tv_title_text = 'module_activity_audioplayer_tv_title_text';
  static const module_activity_audioplayer_tv_audio_name_text = 'module_activity_audioplayer_tv_audio_name_text';
  static const audio_size = 'audio_size';
  static const text_number = 'text_number';
  static const the_other_party_is_not_online = 'the_other_party_is_not_online';
  static const called_unanswered_please_dial_later = 'called_unanswered_please_dial_later';
  static const connection_failure_please_try_again_later = 'connection_failure_please_try_again_later';
  static const please_check_network_availability = 'please_check_network_availability';
  static const delete_group_chart = 'delete_group_chart';
  static const disband_the_group = 'disband_the_group';
  static const disband_the_group_desc = 'disband_the_group_desc';
  static const this_group_has_been_disbanded_by_you = 'this_group_has_been_disbanded_by_you';
  static const the_group_has_been_disbanded_by_the_group_leader = 'the_group_has_been_disbanded_by_the_group_leader';
  static const you_have_been_removed_from_group_chat = 'you_have_been_removed_from_group_chat';
  static const can_not_download_file = 'can_not_download_file';
  static const send_error = 'send_error';
  static const is_calling = 'is_calling';
  static const is_connection = 'is_connection';
  static const temporarily_unable_to_connect = 'temporarily_unable_to_connect';
  static const you_are_already_in_a_call_you_cannot_dial_again = 'you_are_already_in_a_call_you_cannot_dial_again';
  static const please_wait_later = 'please_wait_later';
  static const audio_player_file_size = 'audio_player_file_size';
  static const backup_exit_tip = 'backup_exit_tip';
  static const backup_if_cancel_backup_will_del = 'backup_if_cancel_backup_will_del';
  static const backup_contact_is_backup_waiting = 'backup_contact_is_backup_waiting';
  static const backup_contact_backup_success = 'backup_contact_backup_success';
  static const backup_confirm = 'backup_confirm';
  static const backup_contact_backup_fail = 'backup_contact_backup_fail';
  static const backup_contact_backup_cancel = 'backup_contact_backup_cancel';
  static const backup_file_has_send_please_confirm_success_on_pc = 'backup_file_has_send_please_confirm_success_on_pc';
  static const backup_contact_backup_fail_connect_break = 'backup_contact_backup_fail_connect_break';
  static const backup_contact_backup = 'backup_contact_backup';
  static const backup_fcontact_backup_to_obox = 'backup_fcontact_backup_to_obox';
  static const backup_begin_backup = 'backup_begin_backup';
  static const backup_backup_fail = 'backup_backup_fail';
  static const call_other_side_refuse_your_request = 'call_other_side_refuse_your_request';
  static const call_other_side_invite_video_chat = 'call_other_side_invite_video_chat';
  static const call_unfamiliar_number = 'call_unfamiliar_number';
  static const call_other_side_maybe_offline = 'call_other_side_maybe_offline';
  static const call_record_permission_refuse_and_call_has_end = 'call_record_permission_refuse_and_call_has_end';
  static const call_speaker = 'call_speaker';
  static const call_to_video = 'call_to_video';
  static const call_silence = 'call_silence';
  static const call_memo = 'call_memo';
  static const call_camera = 'call_camera';
  static const call_to_voice = 'call_to_voice';
  static const call_end_call = 'call_end_call';
  static const call_switch_camera = 'call_switch_camera';
  static const camera_please_agree_record_permission_then_reuse_record_function =
      'camera_please_agree_record_permission_then_reuse_record_function';
  static const camera_please_agree_related_permission_then_operate =
      'camera_please_agree_related_permission_then_operate';
  static const chat_service_number = 'chat_service_number';
  static const chat_system_notify = 'chat_system_notify';
  static const chat_file_trans_assist = 'chat_file_trans_assist';
  static const chat_voice_is_connect_can_not_use_this_func = 'chat_voice_is_connect_can_not_use_this_func';
  static const chat_downing = 'chat_downing';
  static const chat_decoding_file = 'chat_decoding_file';
  static const chat_storage_permission_refuse_then_cannot_operate_file =
      'chat_storage_permission_refuse_then_cannot_operate_file';
  static const chat_camera_permission_refused_then_cannot_use_camera =
      'chat_camera_permission_refused_then_cannot_use_camera';
  static const chat_del_confirm = 'chat_del_confirm';
  static const chat_if_confirm_del_this_message = 'chat_if_confirm_del_this_message';
  static const chat_file_has_del = 'chat_file_has_del';
  static const chat_click_look_word = 'chat_click_look_word';
  static const chat_second = 'chat_second';
  static const chat_file_has_expire = 'chat_file_has_expire';
  static const chat_downloaded = 'chat_downloaded';
  static const chat_send_success = 'chat_send_success';
  static const chat_click_download = 'chat_click_download';
  static const chat_downing_2 = 'chat_downing_2';
  static const chat_download_again = 'chat_download_again';
  static const chat_sending = 'chat_sending';
  static const chat_send_fail = 'chat_send_fail';
  static const chat_you_has_removed_form_group_chat = 'chat_you_has_removed_form_group_chat';
  static const chat_add_friend_first_may_other_side_del_you = 'chat_add_friend_first_may_other_side_del_you';
  static const chat_pic_has_del = 'chat_pic_has_del';
  static const chat_send_word_not_allow_exceed = 'chat_send_word_not_allow_exceed';
  static const chat_word = 'chat_word';
  static const chat_record_time_too_short = 'chat_record_time_too_short';
  static const chat_the_file_your_send_not_exit = 'chat_the_file_your_send_not_exit';
  static const chat_file_size_not_exceed_50_m = 'chat_file_size_not_exceed_50_m';
  static const chat_pic_size_not_exceed_20_m = 'chat_pic_size_not_exceed_20_m';
  static const chat_video_had_del = 'chat_video_had_del';
  static const chat_downloading = 'chat_downloading';
  static const chat_voice_chat_not_use_this_fun = 'chat_voice_chat_not_use_this_fun';
  static const chat_downloading_repeat_again_later = 'chat_downloading_repeat_again_later';
  static const chat_file_decode_fail = 'chat_file_decode_fail';
  static const chat_has_copy_to_shear_plate = 'chat_has_copy_to_shear_plate';
  static const chat_has_forward = 'chat_has_forward';
  static const chat_info_chat_setting = 'chat_info_chat_setting';
  static const chat_info_look_for_more_group_member = 'chat_info_look_for_more_group_member';
  static const chat_info_group_chat_name = 'chat_info_group_chat_name';
  static const chat_info_group_chat_describe = 'chat_info_group_chat_describe';
  static const chat_info_group_chat_announcement = 'chat_info_group_chat_announcement';
  static const chat_info_not_name = 'chat_info_not_name';
  static const chat_info_not_describe = 'chat_info_not_describe';
  static const chat_info_group_qr_code = 'chat_info_group_qr_code';
  static const chat_info_my_group_nickname = 'chat_info_my_group_nickname';
  static const chat_info_not_setting = 'chat_info_not_setting';
  static const chat_info_stick_chat = 'chat_info_stick_chat';
  static const chat_info_message_no_disturbing = 'chat_info_message_no_disturbing';
  static const chat_info_clear_chat_record = 'chat_info_clear_chat_record';
  static const chat_info_group_privilege = 'chat_info_group_privilege';
  static const chat_info_del_and_exit = 'chat_info_del_and_exit';
  static const chat_info_touch_phone_and_red_phone_not_support_create_group =
      'chat_info_touch_phone_and_red_phone_not_support_create_group';
  static const chat_info_cleat_confirm = 'chat_info_cleat_confirm';
  static const chat_info_if_confirm_clear_chat_record_with_this_contact =
      'chat_info_if_confirm_clear_chat_record_with_this_contact';
  static const chat_info_group_name_not_allowed_empty_or_blank = 'chat_info_group_name_not_allowed_empty_or_blank';
  static const chat_info_nickname_not_allowed_empty_or_blank = 'chat_info_nickname_not_allowed_empty_or_blank';
  static const chat_info_group_chat_invite_limit = 'chat_info_group_chat_invite_limit';
  static const chat_info_group_owner_has_open_group_chat_limit_you_are_not_allowed_join_group =
      'chat_info_group_owner_has_open_group_chat_limit_you_are_not_allowed_join_group';
  static const chat_info_group_chat_setting = 'chat_info_group_chat_setting';
  static const chat_info_loading_group_info_and_waiting = 'chat_info_loading_group_info_and_waiting';
  static const chat_info_chat_name = 'chat_info_chat_name';
  static const chat_info_group_name_has_lock = 'chat_info_group_name_has_lock';
  static const chat_info_group_owner_locked_group_name_other_not_modify =
      'chat_info_group_owner_locked_group_name_other_not_modify';
  static const chat_info_group_not_modify = 'chat_info_group_not_modify';
  static const chat_info_group_title_not_change = 'chat_info_group_title_not_change';
  static const chat_info_group_describe_not_change = 'chat_info_group_describe_not_change';
  static const chat_info_exit_group_chat = 'chat_info_exit_group_chat';
  static const chat_info_if_confirm_del_and_exit_group = 'chat_info_if_confirm_del_and_exit_group';
  static const chat_info_group_name = 'chat_info_group_name';
  static const chat_info_group_describe = 'chat_info_group_describe';
  static const chat_info_group_announcement = 'chat_info_group_announcement';
  static const chat_info_nickname = 'chat_info_nickname';
  static const chat_info_not_blank_or_empty = 'chat_info_not_blank_or_empty';
  static const chat_info_get_it = 'chat_info_get_it';
  static const chat_info_you_are_abandon_group_owner_confirm_select_nickname_as_new_group_owner =
      'chat_info_you_are_abandon_group_owner_confirm_select_nickname_as_new_group_owner';
  static const chat_info_transfer_group_owner_permission = 'chat_info_transfer_group_owner_permission';
  static const chat_contact_select_group_member = 'chat_contact_select_group_member';
  static const chat_contact_search_contact = 'chat_contact_search_contact';
  static const chat_contact_search_no_result = 'chat_contact_search_no_result';
  static const chat_contact_confirm = 'chat_contact_confirm';
  static const chat_contact_relieve_friend_relate_will_del = 'chat_contact_relieve_friend_relate_will_del';
  static const chat_contact_if_del_relate_info = 'chat_contact_if_del_relate_info';
  static const chat_contact_confirm_del_contact_from_enterprise = 'chat_contact_confirm_del_contact_from_enterprise';
  static const chat_contact_del_confirm = 'chat_contact_del_confirm';
  static const chat_contact_save = 'chat_contact_save';
  static const chat_contact_add_note = 'chat_contact_add_note';
  static const chat_contact_add_contact = 'chat_contact_add_contact';
  static const chat_contact_edit_contact = 'chat_contact_edit_contact';
  static const chat_contact_has_send_add_request = 'chat_contact_has_send_add_request';
  static const chat_contact_modify_success = 'chat_contact_modify_success';
  static const chat_contact_del_friend_success = 'chat_contact_del_friend_success';
  static const chat_contact_number = 'chat_contact_number';
  static const chat_contact_max_11 = 'chat_contact_max_11';
  static const chat_contact_number_not_exit = 'chat_contact_number_not_exit';
  static const chat_contact_note_first_name = 'chat_contact_note_first_name';
  static const chat_contact_note_last_name = 'chat_contact_note_last_name';
  static const chat_contact_default_is_number = 'chat_contact_default_is_number';
  static const chat_contact_unit = 'chat_contact_unit';
  static const chat_contact_please_pass_friend_verify = 'chat_contact_please_pass_friend_verify';
  static const chat_contact_apply_add_friend = 'chat_contact_apply_add_friend';
  static const chat_contact_cancel = 'chat_contact_cancel';
  static const chat_contact_contact_error = 'chat_contact_contact_error';
  static const chat_contact_number_not_allowed_empty = 'chat_contact_number_not_allowed_empty';
  static const chat_contact_cannot_add_own_number = 'chat_contact_cannot_add_own_number';
  static const chat_contact_allow_6_number_and_11_phone_number = 'chat_contact_allow_6_number_and_11_phone_number';
  static const chat_contact_not_allowed_repeat_add = 'chat_contact_not_allowed_repeat_add';
  static const chat_contact_has_add_enterprise_record = 'chat_contact_has_add_enterprise_record';
  static const chat_contact_has_in_friends_not_allowed_repeat_add =
      'chat_contact_has_in_friends_not_allowed_repeat_add';
  static const chat_contact_check_number_fail = 'chat_contact_check_number_fail';
  static const chat_contact_no_change = 'chat_contact_no_change';
  static const chat_contact_del_success = 'chat_contact_del_success';
  static const chat_contact_del_friend_fail = 'chat_contact_del_friend_fail';
  static const chat_contact_confirm_2 = 'chat_contact_confirm_2';
  static const chat_contact_max_allow_select_member = 'chat_contact_max_allow_select_member';
  static const chat_contact_select_all = 'chat_contact_select_all';
  static const chat_contact_selected_number_exceed_limit = 'chat_contact_selected_number_exceed_limit';
  static const chat_contact_select_none = 'chat_contact_select_none';
  static const chat_contact_confirm_3 = 'chat_contact_confirm_3';
  static const chat_contact_group_chat = 'chat_contact_group_chat';
  static const chat_contact_confirm_forward_to = 'chat_contact_confirm_forward_to';
  static const chat_contact_ok = 'chat_contact_ok';
  static const chat_contact_forward_confirm = 'chat_contact_forward_confirm';
  static const chat_contact_file_transfer_assist = 'chat_contact_file_transfer_assist';
  static const chat_contact_my_group_chat = 'chat_contact_my_group_chat';
  static const chat_contact_del = 'chat_contact_del';
  static const chat_save_local = 'chat_save_local';
  static const chat_contact_has_select_enc_file = 'chat_contact_has_select_enc_file';
  static const chat_contact_size = 'chat_contact_size';
  static const chat_contact_group_chat_2 = 'chat_contact_group_chat_2';
  static const chat_contact_send_to = 'chat_contact_send_to';
  static const chat_contact_relax_operate_too_much = 'chat_contact_relax_operate_too_much';
  static const chat_contact_add_to_enterprise_chat_list = 'chat_contact_add_to_enterprise_chat_list';
  static const chat_contact_agree_record_permission = 'chat_contact_agree_record_permission';
  static const chat_contact_not_connect_service = 'chat_contact_not_connect_service';
  static const chat_contact_not_call_myself = 'chat_contact_not_call_myself';
  static const chat_contact_add_number = 'chat_contact_add_number';
  static const chat_contact_number_as_friend = 'chat_contact_number_as_friend';
  static const chat_contact_has_send_friend_add_request = 'chat_contact_has_send_friend_add_request';
  static const dial_record_del_call_record = 'dial_record_del_call_record';
  static const dial_record_cancel_all = 'dial_record_cancel_all';
  static const doc_preview_preview = 'doc_preview_preview';
  static const edit_oudoc_file_editor = 'edit_oudoc_file_editor';
  static const edit_oudoc_dec_fail = 'edit_oudoc_dec_fail';
  static const encryped_file_manager_doc = 'encryped_file_manager_doc';
  static const encryped_file_manager_pic = 'encryped_file_manager_pic';
  static const encryped_file_manager_music = 'encryped_file_manager_music';
  static const encryped_file_manager_video = 'encryped_file_manager_video';
  static const encryped_file_manager_other = 'encryped_file_manager_other';
  static const encryped_file_manager_if_del_enc_file = 'encryped_file_manager_if_del_enc_file';
  static const confirm_delete_encryped_file_list = 'confirm_delete_encryped_file_list';
  static const encryped_file_manager_size = 'encryped_file_manager_size';
  static const encryped_file_manager_del_file = 'encryped_file_manager_del_file';
  static const encryped_file_manager_if_send_file = 'encryped_file_manager_if_send_file';
  static const encryped_file_manager_send_file = 'encryped_file_manager_send_file';
  static const encryped_file_manager_receive_file = 'encryped_file_manager_receive_file';
  static const encryped_file_manager_receive_file_fail = 'encryped_file_manager_receive_file_fail';
  static const group_member_group_member = 'group_member_group_member';
  static const group_member_group_invite_limit = 'group_member_group_invite_limit';
  static const group_member_group_member_2 = 'group_member_group_member_2';
  static const group_member_not_invite_friend_to_group = 'group_member_not_invite_friend_to_group';
  static const help_help = 'help_help';
  static const help_phone_setting = 'help_phone_setting';
  static const help_user_handbook = 'help_user_handbook';
  static const help_answer_to_question = 'help_answer_to_question';
  static const help_contact_backup_to_obox = 'help_contact_backup_to_obox';
  static const help_not_upload_version = 'help_not_upload_version';
  static const help_document = 'help_document';
  static const help_load_fail_repeat_again = 'help_load_fail_repeat_again';
  static const help_launch_setting = 'help_launch_setting';
  static const help_battery_setting = 'help_battery_setting';
  static const login_number = 'login_number';
  static const login_update_success = 'login_update_success';
  static const login_regist_success = 'login_regist_success';
  static const login_number_2 = 'login_number_2';
  static const login_qr_code_not_reuse = 'login_qr_code_not_reuse';
  static const login_login = 'login_login';
  static const login_download_fail = 'login_download_fail';
  static const login_downloading_theme = 'login_downloading_theme';
  static String main_contact = 'main_contact';
  static const main_browser = 'main_browser';
  static const main_dao = 'main_dao';
  static const main_dial = 'main_dial';
  static const main_message = 'main_message';
  static const main_about = 'main_about';
  static const main_collections = 'main_collections';
  static const main_account_tip = 'main_account_tip';
  static const main_regist_code_error_clear_data_tip = 'main_regist_code_error_clear_data_tip';
  static const main_account_error_clear_data_tip = 'main_account_error_clear_data_tip';
  static const main_downloading_skin = 'main_downloading_skin';
  static const main_no_feedback_content = 'main_no_feedback_content';
  static const main_your_tip_summit_success = 'main_your_tip_summit_success';
  static const main_your_tip_summit_fail = 'main_your_tip_summit_fail';
  static const main_metatel_setting = 'main_metatel_setting';
  static const main_unlock_password = 'main_unlock_password';
  static const main_file_manage = 'main_file_manage';
  static const privacy_security = 'privacy_security';
  static const main_feedback_suggest = 'main_feedback_suggest';
  static const main_help = 'main_help';
  static const network_node = 'network_node';
  static const main_version_update = 'main_version_update';
  static const main_logout = 'main_logout';
  static const main_number = 'main_number';
  static const main_feedback = 'main_feedback';
  static const main_please_input_feedback_suggest = 'main_please_input_feedback_suggest';
  static const main_public = 'main_public';
  static const main_cancel_stick_fail = 'main_cancel_stick_fail';
  static const main_stick_fail = 'main_stick_fail';
  static const main_confirm_clear_service_message = 'main_confirm_clear_service_message';
  static const main_confirm_del_message = 'main_confirm_del_message';
  static const main_total = 'main_total';
  static const main_number_contact = 'main_number_contact';
  static const main_call_and_not_use_func = 'main_call_and_not_use_func';
  static const main_add_friend = 'main_add_friend';
  static const main_scan = 'main_scan';
  static const main_if_del_friend_req = 'main_if_del_friend_req';
  static const main_agree = 'main_agree';
  static const main_has_agree = 'main_has_agree';
  static const main_agree_failed = 'main_agree_failed';
  static const main_del_error = 'main_del_error';
  static const main_invalid_qr = 'main_invalid_qr';
  static const main_enterprise_friend = 'main_enterprise_friend';
  static const main_my_friend = 'main_my_friend';
  static const main_my_group = 'main_my_group';
  static const main_my_channel = 'main_my_channel';
  static const main_friend_req = 'main_friend_req';
  static const main_voicemeeting_title = 'main_voicemeeting_title';
  static const main_group_message_tip = 'main_group_message_tip';
  static const main_not_connect_service = 'main_not_connect_service';
  static const main_not_contain = 'main_not_contain';
  static const main_add_number = 'main_add_number';
  static const main_user_as_friend = 'main_user_as_friend';
  static const voice_meeting_voice_meeting = 'voice_meeting_voice_meeting';
  static const voice_meeting_meeting_theme = 'voice_meeting_meeting_theme';
  static const voice_meeting_option = 'voice_meeting_option';
  static const voice_meeting_join_member = 'voice_meeting_join_member';
  static const voice_meeting_begin_call = 'voice_meeting_begin_call';
  static const voice_meeting_create_meeting_fail_retry = 'voice_meeting_create_meeting_fail_retry';
  static const voice_meeting_recall = 'voice_meeting_recall';
  static const voice_meeting_voice_meeting_notify = 'voice_meeting_voice_meeting_notify';
  static const voice_meeting_meeting_end_if_retry = 'voice_meeting_meeting_end_if_retry';
  static const voice_meeting_meeting_if_join = 'voice_meeting_meeting_if_join';
  static const voice_meeting_net_not_avail = 'voice_meeting_net_not_avail';
  static const voice_meeting_please_agree_voice_permission = 'voice_meeting_please_agree_voice_permission';
  static const message_dialog_forward = 'message_dialog_forward';
  static const message_dialog_translate = 'message_dialog_translate';
  static const message_dialog_un_translate = 'message_dialog_un_translate';

  static const password_check_input_password = 'password_check_input_password';
  static const password_check_password_error = 'password_check_password_error';
  static const password_check_retry_later = 'password_check_retry_later';
  static const password_check_retry_later_hour = 'password_check_retry_later_hour';
  static const password_check_input_password_error = 'password_check_input_password_error';
  static const password_check_restore_unlock_password = 'password_check_restore_unlock_password';
  static const password_check_tip_error = 'password_check_tip_error';
  static const password_check_answer = 'password_check_answer';
  static const password_check_input_answer = 'password_check_input_answer';
  static const password_check_reset_password = 'password_check_reset_password';
  static const password_check_blank_del_password = 'password_check_blank_del_password';
  static const password_check_input_again = 'password_check_input_again';
  static const password_check_repeat_input_6_password = 'password_check_repeat_input_6_password';
  static const password_check_confirm = 'password_check_confirm';
  static const password_check_reset_success = 'password_check_reset_success';
  static const password_check_length_6 = 'password_check_length_6';
  static const password_check_password_not_same = 'password_check_password_not_same';
  static const password_check_set_unlock_password = 'password_check_set_unlock_password';
  static const password_check_old_unlock_password = 'password_check_old_unlock_password';
  static const password_check_input_6_number = 'password_check_input_6_number';
  static const password_check_unlock_password = 'password_check_unlock_password';
  static const password_check_password_tip = 'password_check_password_tip';
  static const password_check_use_restore_password = 'password_check_use_restore_password';
  static const password_check_can_select = 'password_check_can_select';
  static const password_check_input_password_6 = 'password_check_input_password_6';
  static const password_check_modify_password = 'password_check_modify_password';
  static const password_check_setting_unlock_password = 'password_check_setting_unlock_password';
  static const password_check_new_unlock_password = 'password_check_new_unlock_password';
  static const password_check_blank_del_password_2 = 'password_check_blank_del_password_2';
  static const password_check_password_modify_success = 'password_check_password_modify_success';
  static const password_check_password_set_success = 'password_check_password_set_success';
  static const password_check_please_input_answer = 'password_check_please_input_answer';
  static const password_check_old_password_error = 'password_check_old_password_error';
  static const pc_login_pc_confirm_login = 'pc_login_pc_confirm_login';
  static const pc_login_confirm_login = 'pc_login_confirm_login';
  static const pc_login_cancel_login = 'pc_login_cancel_login';
  static const pc_login_confirm_agree_fail = 'pc_login_confirm_agree_fail';
  static const pc_login_qr_overdue = 'pc_login_qr_overdue';
  static const pc_login_confirm_agree_success = 'pc_login_confirm_agree_success';
  static const pc_login_refuse_agree_fail = 'pc_login_refuse_agree_fail';
  static const pc_login_tip = 'pc_login_tip';
  static const pc_login_if_exit_windows_number = 'pc_login_if_exit_windows_number';
  static const photo_burn_pic_load_error_retry = 'photo_burn_pic_load_error_retry';
  static const push_lack_param = 'push_lack_param';
  static const qr_code_abandon_modify_data = 'qr_code_abandon_modify_data';
  static const qr_code_abandon_back_up = 'qr_code_abandon_back_up';
  static const qr_code_abandon = 'qr_code_abandon';
  static const qr_code_no_abandon = 'qr_code_no_abandon';
  static const qr_code_number = 'qr_code_number';
  static const qr_code_name_not_empty = 'qr_code_name_not_empty';
  static const qr_code_edit_qr_content = 'qr_code_edit_qr_content';
  static const qr_code_group_qr = 'qr_code_group_qr';
  static const qr_code_scan_join = 'qr_code_scan_join';
  static const recover_contact_restore = 'recover_contact_restore';
  static const recover_contact_restore_to_phone = 'recover_contact_restore_to_phone';
  static const recover_start_restore = 'recover_start_restore';
  static const recover_quit_tip = 'recover_quit_tip';
  static const recover_back_may_lose_data = 'recover_back_may_lose_data';
  static const recover_restore_waiting = 'recover_restore_waiting';
  static const recover_restore_fail = 'recover_restore_fail';
  static const recover_restore_cancel = 'recover_restore_cancel';
  static const recover_restore_success = 'recover_restore_success';
  static const recover_restore_fail_disconnect = 'recover_restore_fail_disconnect';
  static const recover_restore_fail_2 = 'recover_restore_fail_2';
  static const regist_6_number = 'regist_6_number';
  static const regist_regist_success = 'regist_regist_success';
  static const regist_input_right_number = 'regist_input_right_number';
  static const regist_please_send_code = 'regist_please_send_code';
  static const regist_input_right_verify_code = 'regist_input_right_verify_code';
  static const regist_input_6_password = 'regist_input_6_password';
  static const regist_regist_fail = 'regist_regist_fail';
  static const regist_confirm_select = 'regist_confirm_select';
  static const regist_as_number = 'regist_as_number';
  static const regist_number_confirm = 'regist_number_confirm';
  static const ringtone_setting = 'ringtone_setting';
  static const ringtone_restore_default = 'ringtone_restore_default';
  static const ringtone_ringtone = 'ringtone_ringtone';
  static const ringtone_music = 'ringtone_music';
  static const ringtone_name = 'ringtone_name';
  static const ringtone_set_success = 'ringtone_set_success';
  static const search_search = 'search_search';
  static const search_input_content = 'search_input_content';
  static const search_no_result = 'search_no_result';
  static const search_public = 'search_public';
  static const search_look_more = 'search_look_more';
  static const search_record = 'search_record';
  static const search_call = 'search_call';
  static const search_chat = 'search_chat';
  static const search_group_chat = 'search_group_chat';
  static const search_fumc = 'search_fumc';
  static const main_setting = 'main_setting';
  static const main_mine = 'main_mine';

  static const setting_message_voice_tip = 'setting_message_voice_tip';
  static const setting_vibrate_tip = 'setting_vibrate_tip';
  static const setting_security_mode = 'setting_security_mode';
  static const setting_ringtone_setting = 'setting_ringtone_setting';
  static const setting_theme_setting = 'setting_theme_setting';
  static const setting_del_all = 'setting_del_all';
  static const setting_if_del_all = 'setting_if_del_all';
  static const setting_default_theme = 'setting_default_theme';
  static const setting_del_success = 'setting_del_success';
  static const setting_del_fail = 'setting_del_fail';
  static const share_creat_new_chat = 'share_creat_new_chat';
  static const share_select_friend = 'share_select_friend';
  static const share_latest = 'share_latest';
  static const share_share_to = 'share_share_to';
  static const share_share_meeting = 'share_share_meeting';
  static const share_not_support = 'share_not_support';
  static const splash_check_net = 'splash_check_net';
  static const splash_has_reset_clear_data = 'splash_has_reset_clear_data';
  static const splash_has_erroe_clear_data = 'splash_has_erroe_clear_data';
  static const splash_account_tip = 'splash_account_tip';
  static const splash_has_update = 'splash_has_update';
  static const splash_updating = 'splash_updating';
  static const splash_restart = 'splash_restart';
  static const splash_agree_permission = 'splash_agree_permission';
  static const splash_update_fail_repeat = 'splash_update_fail_repeat';
  static const theme_setting = 'theme_setting';
  static const video_pre = 'video_pre';
  static const theme_file_is_exit = 'theme_file_is_exit';
  static const weh_reload = 'weh_reload';
  static const weh_retry = 'weh_retry';
  static const web_rtc_invite_video_chat = 'web_rtc_invite_video_chat';
  static const web_rtc_refuse = 'web_rtc_refuse';
  static const web_rtc_permission_refuse_end_call = 'web_rtc_permission_refuse_end_call';
  static const capture_qr = 'capture_qr';
  static const capture_scan_qr = 'capture_scan_qr';
  static const capture_input_qr_number = 'capture_input_qr_number';
  static const capture_open_light = 'capture_open_light';
  static const capture_reopen = 'capture_reopen';
  static const capture_max_40 = 'capture_max_40';
  static const capture_number_no_empty = 'capture_number_no_empty';
  static const capture_file_not_pic = 'capture_file_not_pic';
  static const capture_scan_fail_retry = 'capture_scan_fail_retry';
  static const capture_qr_invalid = 'capture_qr_invalid';
  static const other_calling = 'other_calling';
  static const other_not_online_retry_later = 'other_not_online_retry_later';
  static const other_connect_error_retry_later = 'other_connect_error_retry_later';
  static const other_user_not_online = 'other_user_not_online';
  static const other_call_fail = 'other_call_fail';
  static const other_you = 'other_you';
  static const other_you_small = 'other_you_small';
  static const other_second = 'other_second';
  static const other_open = 'other_open';
  static const other_clear_msg = 'other_clear_msg';
  static const other_reset_unread = 'other_reset_unread';
  static const other_reset_has_read = 'other_reset_has_read';
  static const other_cancel_stick = 'other_cancel_stick';
  static const other_stick = 'other_stick';
  static const other_del_chat = 'other_del_chat';
  static const other_copy = 'other_copy';
  static const copy_success = 'copy_success';

  static const other_repeal = 'other_repeal';
  static const other_confirm_del = 'other_confirm_del';
  static const other_not_support_file_format = 'other_not_support_file_format';
  static const other_max_input_15 = 'other_max_input_15';
  static const other_max_input_40 = 'other_max_input_40';
  static const other_updating = 'other_updating';
  static const other_not_close_page = 'other_not_close_page';
  static const other_loading = 'other_loading';
  static const other_no_support_this_type_file = 'other_no_support_this_type_file';
  static const other_default_ring = 'other_default_ring';
  static const other_giving = 'other_giving';
  static const other_dial = 'other_dial';
  static const other_coming_call = 'other_coming_call';
  static const other_calling_2 = 'other_calling_2';
  static const other_metatel_call = 'other_metatel_call';
  static const other_with = 'other_with';
  static const other_calling_3 = 'other_calling_3';
  static const other_metatel = 'other_metatel';
  static const other_invite_join_voice_meeting = 'other_invite_join_voice_meeting';
  static const other_not_online_ = 'other_not_online_';
  static const other_add_friend = 'other_add_friend';
  static const other_enterprise_public = 'other_enterprise_public';
  static const other_has_message = 'other_has_message';
  static const other_net_unusable = 'other_net_unusable';
  static const other_error = 'other_error';
  static const other_secret_manage_error = 'other_secret_manage_error';
  static const other_config_error_metatel_will_exit = 'other_config_error_metatel_will_exit';
  static const other_param_error_then_exit = 'other_param_error_then_exit';
  static const other_id_not_exit = 'other_id_not_exit';
  static const other_service_config_error = 'other_service_config_error';
  static const other_metatel_will_exit = 'other_metatel_will_exit';
  static const other_not_find_config_will_exit = 'other_not_find_config_will_exit';
  static const other_not_call_self = 'other_not_call_self';
  static const other_modify_group_name_as = 'other_modify_group_name_as';
  static const other_invite = 'other_invite';
  static const other_with_2 = 'other_with_2';
  static const other_join_group_chat = 'other_join_group_chat';
  static const join_group_chat = 'join_group_chat';
  static const other_invite_2 = 'other_invite_2';
  static const other_remove_by_group_owner = 'other_remove_by_group_owner';
  static const other_has = 'other_has';
  static const other_remove_from_group = 'other_remove_from_group';
  static const other_you_invite = 'other_you_invite';
  static const other_join_group = 'other_join_group';
  static const other_invite_join_group = 'other_invite_join_group';
  static const other_group_owner = 'other_group_owner';
  static const other_has_lock_group_name = 'other_has_lock_group_name';
  static const other_lock_group_name_cancel = 'other_lock_group_name_cancel';
  static const other_has_limit = 'other_has_limit';
  static const other_has_close_limit = 'other_has_close_limit';
  static const other_as_new_group_owner = 'other_as_new_group_owner';
  static const other_by_scan = 'other_by_scan';
  static const other_join_group_by_code = 'other_join_group_by_code';
  static const other_has_exit_group = 'other_has_exit_group';
  static const other_pass_verify = 'other_pass_verify';
  static const other_connect_error_retry = 'other_connect_error_retry';
  static const other_not_take_pic = 'other_not_take_pic';
  static const other_file_open_fail_retry = 'other_file_open_fail_retry';
  static const other_not_support_multi_show = 'other_not_support_multi_show';
  static const other_download_complete = 'other_download_complete';
  static const other_not_support_headup = 'other_not_support_headup';
  static const other_read_dismiss = 'other_read_dismiss';
  static const other_voice_message = 'other_voice_message';
  static const other_pic_message = 'other_pic_message';
  static const other_video_message = 'other_video_message';
  static const other_file_message = 'other_file_message';
  static const other_chat_history = 'other_chat_history';
  static const other_chat_card = 'other_chat_card';
  static const other_pic_word_message = 'other_pic_word_message';
  static const other_calendar_message = 'other_calendar_message';
  static const other_time = 'other_time';
  static const other_second_2 = 'other_second_2';
  static const other_release_cancel_send = 'other_release_cancel_send';
  static const other_finger_move_up_cancel_send = 'other_finger_move_up_cancel_send';
  static const other_record_too_short = 'other_record_too_short';
  static const other_release_send = 'other_release_send';
  static const other_press_said = 'other_press_said';
  static const other_connect_fail_click_refresh = 'other_connect_fail_click_refresh';
  static const other_connect_error_repeat = 'other_connect_error_repeat';
  static const other_net_not_available_check_net = 'other_net_not_available_check_net';
  static const other_check_net_available = 'other_check_net_available';
  static const other_retry_waiting = 'other_retry_waiting';
  static const other_connect_service = 'other_connect_service';
  static const other_at_max = 'other_at_max';
  static const other_word = 'other_word';
  static const other_forget_password = 'other_forget_password';
  static const other_del = 'other_del';
  static const other_can_said_time = 'other_can_said_time';
  static const other_click_notify = 'other_click_notify';
  static const other_friend_detail = 'other_friend_detail';
  static const other_manage_right_trans = 'other_manage_right_trans';
  static const other_lock_group_name = 'other_lock_group_name';
  static const other_lock_other_not_modify = 'other_lock_other_not_modify';
  static const other_only_group_owner_can_invite = 'other_only_group_owner_can_invite';
  static const other_windows_login = 'other_windows_login';
  static const other_not_operate_self_can_exit = 'other_not_operate_self_can_exit';
  static const other_trans_file = 'other_trans_file';
  static const other_exit_by_key = 'other_exit_by_key';
  static const other_pic_pre = 'other_pic_pre';
  static const other_send = 'other_send';
  static const other_take_pic = 'other_take_pic';
  static const other_connect_not_avail = 'other_connect_not_avail';
  static const other_read_dismiss_2 = 'other_read_dismiss_2';
  static const other_file = 'other_file';
  static const other_note = 'other_note';
  static const other_default_number = 'other_default_number';
  static const other_company = 'other_company';
  static const other_unit = 'other_unit';
  static const other_photo_album = 'other_photo_album';
  static const other_preview = 'other_preview';
  static const other_note_2 = 'other_note_2';
  static const other_number = 'other_number';
  static const other_accept = 'other_accept';
  static const accpted = 'accpted';
  static const other_look_for_more_contact = 'other_look_for_more_contact';
  static const other_invite_video_connect = 'other_invite_video_connect';
  static const other_name = 'other_name';
  static const other_click_take_photo = 'other_click_take_photo';
  static const other_name_touch_take_photo_press_take_movie_2 = 'other_name_touch_take_photo_press_take_movie_2';
  static const other_no_relate_contact = 'other_no_relate_contact';
  static const other_no_relate_group_contact = 'other_no_relate_group_contact';
  static const other_no_friend_request = 'other_no_friend_request';
  static const other_no_meeting_record = 'other_no_meeting_record';
  static const other_total = 'other_total';
  static const other_number_group = 'other_number_group';
  static const password_not_empty = 'password_not_empty';
  static const input_password_again = 'input_password_again';
  static const no_customer = 'no_customer';
  static const button_create_voice_meeting = 'button_create_voice_meeting';
  static const button_create_video_meeting = 'button_create_video_meeting';
  static const video_meeting_title = 'video_meeting_title';
  static const confirm_delete_friend_verify = 'confirm_delete_friend_verify';
  static const enter_conference = 'enter_conference';
  static const video_meeting_state_notify = 'video_meeting_state_notify';
  static const video_meeting_create_error = 'video_meeting_create_error';
  static const enter_meeting_progress = 'enter_meeting_progress';
  static const create_video_meeting_error = 'create_video_meeting_error';
  static const video_meeting_network_error = 'video_meeting_network_error';
  static const in_other_meeting_error = 'in_other_meeting_error';
  static const video_meeting_is_full = 'video_meeting_is_full';
  static const video_meeting_miss = 'video_meeting_miss';
  static const create_group_txt = 'create_group_txt';
  static const create_private_group_txt = 'create_private_group_txt';

  static const delete_call_log_confirm = 'delete_call_log_confirm';
  static const delete_call_log_progress = 'delete_call_log_progress';
  static const connect_customer = 'connect_customer';
  static const original_pic = 'original_pic';
  static const metatel_id = 'metatel_id';
  static const register_has_bind = 'register_has_bind';
  static const register_code_error = 'register_code_error';
  static const register_code_invalid = 'register_code_invalid';
  static const number_rush_registration = 'number_rush_registration';
  static const number_bind_fail = 'number_bind_fail';
  static const code_error = 'code_error';
  static const you_where_mentioned = 'you_where_mentioned';
  static const call_out = 'call_out';
  static const call_in = 'call_in';
  static const no_second = 'no_second';
  static const hour = 'hours';
  static const minutes = 'minutes';
  static const other_second_3 = 'other_second_3';
  static const include = 'include';
  static const draft = 'draft';
  static const other_voice_message_2 = 'other_voice_message_2';
  static const chat_info_clear_chat_record_success = 'chat_info_clear_chat_record_success';
  static const chat_info_clear_chat_record_fail = 'chat_info_clear_chat_record_fail';
  static const file_not_exit = 'file_not_exit';
  static const decrypting_file_in_progress_hold_on = 'decrypting_file_in_progress_hold_on';
  static const the_network_is_not_available_please_try_again = 'the_network_is_not_available_please_try_again';
  static const unreachable = 'unreachable';
  static const parameter_inconsistency = 'parameter_inconsistency';
  static const fail_to_get_the_theme = 'fail_to_get_the_theme';
  static const self = 'self';
  static const share_to = 'share_to';
  static const related_messages = 'related_messages';
  static const scan_qr = 'scan_qr';
  static const check_file_if_exit = 'check_file_if_exit';
  static const unknown = 'unknown';
  static const network_time_out = 'network_time_out';
  static const network_unavailable = 'network_unavailable';
  static const service_unavailable = 'service_unavailable';
  static const source_unavailable = 'source_unavailable';
  static const return_is_null = 'return_is_null';
  static const too_short = 'too_short';
  static const know = 'know';
  static const view_detail = 'view_detail';
  static const uni_app = 'uni_app';
  static const schedule = 'schedule';
  static const talkback = 'talkback';
  static const sharing_platform = 'sharing_platform';
  static const approval = 'approval';
  static const temporary_not_open = 'temporary_not_open';
  static const add = 'add';
  static const complete = 'complete';
  static const canceled = 'canceled';
  static const opposite = 'opposite';
  static const call_duration = 'call_duration';
  static const me = 'me';
  static const send_msg = 'send_msg';
  static const id = 'id';
  static const name = 'name';
  static const no_company_contactor = 'no_company_contactor';
  static const title_system_setting = 'title_system_setting';
  static const system_setting_content = 'system_setting_content';
  static const system_setting_overlays = 'system_setting_overlays';
  static const ignore = 'ignore';
  static const uni_tip_title = 'uni_tip_title';
  static const uni_tip_content = 'uni_tip_content';
  static const uni_all = 'uni_all';
  static const uni_common = 'uni_common';
  static const the_other_party_mobile_phone_may_not_be_around_please_try_again_later =
      'the_other_party_mobile_phone_may_not_be_around_please_try_again_later';
  static const tip_title = 'tip_title';
  static const inviated_tip = 'inviated_tip';
  static const transport_pc_data_failed = 'transport_pc_data_failed';
  static const please_enter_your_password = 'please_enter_your_password';
  static const confirm = 'confirm';
  static const please_input_user_nickname = 'please_input_user_nickname';
  static const contact_info = 'contact_info';
  static const edit = 'edit';
  static const add_buddy = 'add_buddy';
  static const media_links_and_docs = 'media_links_and_docs';
  static const group_photo = 'group_photo';
  static const background = 'background';
  static const share_contactor = 'share_contactor';
  static const search_dialog = 'search_dialog';
  static const mute = 'mute';
  static const block_user = 'block_user';
  static const blocked = 'blocked';
  static const clear_dialog = 'clear_dialog';
  static const delete_contact = 'delete_contact';
  static const select = 'select';
  static const photo = 'photo';
  static const no_content = 'no_content';
  static const exiting_a_Group = 'exiting_a_Group';
  static const other_members_of_the_group_are_notified_when_the_group_name_is_changed =
      'other_members_of_the_group_are_notified_when_the_group_name_is_changed';
  static const other_members_of_the_group_are_notified_when_the_group_describe_is_changed =
      'other_members_of_the_group_are_notified_when_the_group_describe_is_changed';
  static const other_members_of_the_group_are_notified_when_the_group_announcement_is_changed =
      'other_members_of_the_group_are_notified_when_the_group_announcement_is_changed';
  static const i_am_a_group_chat_name = 'i_am_a_group_chat_name';
  static const i_am_a_group_chat_describe = 'i_am_a_group_chat_describe';
  static const description = 'description';
  static const blacklist = 'blacklist';
  static const voice_phone = 'voice_phone';
  static const video_phone = 'video_phone';
  static const setting_data_storage = 'setting_data_storage';
  static const setting_notification = 'setting_notification';
  static const edit_user_icon = 'edit_user_icon';
  static const capture_picture = 'capture_picture';
  static const get_picture_from_phone = 'get_picture_from_phone';
  static const save_photo = 'save_photo';
  static const quit_group = 'quit_group';
  static const dissolution_group = 'dissolution_group';
  static const dissolution = 'dissolution';
  static const other_update_group_info = 'other_update_group_info';
  static const select_contact = 'select_contact';
  static const create = 'create';
  static const members = 'members';
  static const members_num = 'members_num';
  static const chat_img_video = 'chat_img_video';
  static const chat_camera = 'chat_camera';
  static const chat_img = 'chat_img';
  static const chat_camera_pic = 'chat_camera_pic';
  static const call_terminate = 'call_terminate';
  static const invite_you_video_call = 'invite_you_video_call';
  static const invite_you_audio_call = 'invite_you_audio_call';
  static const network_text = 'network_text';
  static const other_multiple_selection = 'other_multiple_selection';
  static const personal_data = 'personal_data';
  static const personal_data_profile_photo = 'personal_data_profile_photo';
  static const personal_data_name = 'personal_data_name';
  static const personal_data_my_qr_code = 'personal_data_my_qr_code';
  static const one_by_one_forward = 'one_by_one_forward';
  static const combine_and_forward = 'combine_and_forward';
  static const replay = 'replay';
  static const login_fail = 'login_fail';
  static const audio = 'audio';
  static const select_a_group = 'select_a_group';
  static const forward_to = 'forward_to';
  static const marge_msg_history = 'marge_msg_history';
  static const chat_msg_history = 'chat_msg_history';
  static const latest_unread_msg = 'latest_unread_msg';
  static const continue_ = 'continue_';
  static const forword_tips = 'forword_tips';
  static const max_select_20 = 'max_select_20';
  static const waiting = 'waiting';
  static const the_video = 'the_video';
  static const the_videos = 'the_videos';
  static const the_file = 'the_file';
  static const the_picture = 'the_picture';
  static const has_save_to = 'has_save_to';
  static const contact_card = 'contact_card';
  static const chat_item_pin = 'chat_item_pin';
  static const you_missed_call = 'you_missed_call';
  static const welcome = 'welcome';
  static const create_you_account = 'create_you_account';
  static const please_input_user_phone_number = 'please_input_user_phone_number';
  static const register_info = 'register_info';
  static const read_and_agree = 'read_and_agree';
  static const user_service_agreement = 'user_service_agreement';
  static const rivacy_clause = 'rivacy_clause';
  static const send_sms_verification_code = 'send_sms_verification_code';
  static const please_input_sms_code = 'please_input_sms_code';
  static const sms_code_info = 'sms_code_info';
  static const resend_after = 'resend_after';
  static const reset_resend_after = 'reset_resend_after';
  static const please_user_service_agreement = 'please_user_service_agreement';
  static const search_user = 'search_user';
  static const scan_qr_code_to_add = 'scan_qr_code_to_add';
  static const find_metatel_user = 'find_metatel_user';
  static const can_not_add_myself_friend = 'can_not_add_myself_friend';
  static const can_not_search_the_contactor = 'can_not_search_the_contactor';
  static const hwpush_ability_value = 'hwpush_ability_value';
  static const if_delete_choice_msg = 'if_delete_choice_msg';
  static const main_title_group = 'main_title_group';
  static const change_password_need_new_password = 'change_password_need_new_password';
  static const search_uni = 'search_uni';
  static const other_has_call = 'other_has_call';
  static const send_sms_error = 'send_sms_error';
  static const hw_security = 'hw_security';
  static const disable_warning = 'disable_warning';
  static const title_permissions = 'title_permissions';
  static const read_statement = 'read_statement';
  static const accept_btn = 'accept_btn';
  static const exit_btn = 'exit_btn';
  static const title_license = 'title_license';
  static const capture_scan_qr_4_rabbit = 'capture_scan_qr_4_rabbit';
  static const regist_as_number_4_rabbit = 'regist_as_number_4_rabbit';
  static const login_regist_success_4_rabbit = 'login_regist_success_4_rabbit';
  static const login_update_success_4_rabbit = 'login_update_success_4_rabbit';
  static const module_activity_encrypt_file_tv_title_text_4_rabbit =
      'module_activity_encrypt_file_tv_title_text_4_rabbit';
  static const metatel_server_number_4_rabbit = 'metatel_server_number_4_rabbit';
  static const chat_service_number_4_rabbit = 'chat_service_number_4_rabbit';
  static const metatel_file_assistant_4_rabbit = 'metatel_file_assistant_4_rabbit';
  static const other_has_message_4_rabbit = 'other_has_message_4_rabbit';
  static const chat_contact_number_not_exit_4_rabbit = 'chat_contact_number_not_exit_4_rabbit';
  static const chat_contact_default_is_number_4_rabbit = 'chat_contact_default_is_number_4_rabbit';
  static const other_pass_verify_4_rabbit = 'other_pass_verify_4_rabbit';
  static const chat_contact_allow_6_number_and_11_phone_number_4_rabbit =
      'chat_contact_allow_6_number_and_11_phone_number_4_rabbit';
  static const qr_code_number_4_rabbit = 'qr_code_number_4_rabbit';
  static const application_in_progress = 'application_in_progress';
  static const qrcode_edit_4_rabbit = 'qrcode_edit_4_rabbit';
  static const metatel_customer = 'metatel_customer';
  static const transport_pc_data_success = 'transport_pc_data_success';
  static const password_setting = 'password_setting';
  static const unlock_password = 'unlock_password';
  static const unlock_finger = 'unlock_finger';
  static const password_check = 'password_check';
  static const destory_setting = 'destory_setting';
  static const destory_tip = 'destory_tip';
  static const destroy_switch = 'destroy_switch';
  static const destory_password = 'destory_password';
  static const destory_tip_title = 'destory_tip_title';
  static const destory_tip_content = 'destory_tip_content';
  static const update_destory_password = 'update_destory_password';
  static const set_destory_password = 'set_destory_password';
  static const destory_prefix_password = 'destory_prefix_password';
  static const old_destory_prefix_password = 'old_destory_prefix_password';
  static const new_destory_prefix_password = 'new_destory_prefix_password';
  static const destory_password_not_empty = 'destory_password_not_empty';
  static const destory_password_check_length_6 = 'destory_password_check_length_6';
  static const input_destory_password_again = 'input_destory_password_again';
  static const no_same_2_screen_password = 'no_same_2_screen_password';
  static const password_check_title = 'password_check_title';
  static const destory_password_update_successed = 'destory_password_update_successed';
  static const no_same_2_destory_password = 'no_same_2_destory_password';
  static const tip_screen_password_first = 'tip_screen_password_first';
  static const low_version_not_support_finger_print = 'low_version_not_support_finger_print';
  static const your_phone_not_support_finger_print = 'your_phone_not_support_finger_print';
  static const add_finger_print = 'add_finger_print';
  static const need_finger_print = 'need_finger_print';
  static const verify_finger_print = 'verify_finger_print';
  static const verify_finger_print_fail_try_again = 'verify_finger_print_fail_try_again';
  static const verify_finger_print_fail_try_again_later = 'verify_finger_print_fail_try_again_later';
  static const user_finger_print = 'user_finger_print';
  static const tip_set_finger_print = 'tip_set_finger_print';
  static const new_msg_tip = 'new_msg_tip';
  static const please_select_item = 'please_select_item';
  static const please_select_share_item = 'please_select_share_item';
  static const share_tip = 'share_tip';
  static const please_select_valid_item = 'please_select_valid_item';
  static const destory_no_avaliable = 'destory_no_avaliable';
  static const load_fail_alert_retry = 'load_fail_alert_retry';
  static const loading = 'loading';
  static const tips = 'tips';
  static const enterprise_number = 'enterprise_number';
  static const connection_failure_control = 'connection_failure_control';
  static const module_activity_boot_set_alar_text = 'module_activity_boot_set_alar_text';
  static const http_request_error = 'http_request_error';
  static const password_no_same_to_old = 'password_no_same_to_old';
  static const clear_uni_app = 'clear_uni_app';
  static const clear_all_store_files = 'clear_all_store_files';
  static const clear_company_book = 'clear_company_book';
  static const clear_friend_book = 'clear_friend_book';
  static const marketing_notice = 'marketing_notice';
  static const not_find_boot = 'not_find_boot';
  static const it_is_currently_the_default_theme = 'it_is_currently_the_default_theme';
  static const tbs_is_update = 'tbs_is_update';
  static const web_rtc_meeting_permission_refuse = 'web_rtc_meeting_permission_refuse';
  static const camera_permission_refuse_end_call = 'camera_permission_refuse_end_call';
  static const pc_export_file_confirm = 'pc_export_file_confirm';
  static const pc_export_confirm = 'pc_export_confirm';
  static const pc_export_cancel = 'pc_export_cancel';
  static const channel_call_of_notification = 'channel_call_of_notification';
  static const channel_msg_of_notification = 'channel_msg_of_notification';
  static const chat_audio_permission_refused_then_cannot_use_camera =
      'chat_audio_permission_refused_then_cannot_use_camera';
  static const chat_info_if_confirm_clear_chat_group_record_with_this_contact =
      'chat_info_if_confirm_clear_chat_group_record_with_this_contact';
  static const channel_description_of_call_notification = 'channel_description_of_call_notification';
  static const finished = 'finished';
  static const nick_name_not_empty = 'nick_name_not_empty';
  static const edit_user_name = 'edit_user_name';
  static const init_camera_fail = 'init_camera_fail';
  static const picture_tailor_error = 'picture_tailor_error';
  static const access_exception = 'access_exception';
  static const camera_permission_not_open = 'camera_permission_not_open';
  static const camera_permission_not_open_ = 'camera_permission_not_open_';
  static const camera_or_write_permission_not_open = 'camera_or_write_permission_not_open';
  static const un_msg_user = 'un_msg_user';
  static const un_msg_id = 'un_msg_id';
  static const re_edit = 're_edit';
  static const user_phone_number_type = 'user_phone_number_type';
  static const retracted_a_msg = 'retracted_a_msg';
  static const real_authenticate = 'real_authenticate';
  static const please_input_name = 'please_input_name';
  static const please_input_id_card = 'please_input_id_card';
  static const go_authenticate = 'go_authenticate';
  static const identity_authenticate = 'identity_authenticate';
  static const take_a_photo_of_your_own_face_please_make_sure_you_are_facing_the_phone_and_there_is_sufficient_light =
      'take_a_photo_of_your_own_face_please_make_sure_you_are_facing_the_phone_and_there_is_sufficient_light';
  static const gather_my_face = 'gather_my_face';
  static const account = 'account';
  static const about_account = 'about_account';
  static const create_account = 'create_account';
  static const first_use_account = 'first_use_account';
  static const restore_account = 'restore_account';
  static const had_account_mnemonic = 'had_account_mnemonic';
  static const mnemonic = 'mnemonic';
  static const mnemonic_store = 'mnemonic_store';
  static const private_key = 'private_key';
  static const soon_login = 'soon_login';
  static const please_enter_mnemonic_words_separated_by_spaces = 'please_enter_mnemonic_words_separated_by_spaces';
  static const restore_identity = 'restore_identity';
  static const please_enter_the_identity_credentials = 'please_enter_the_identity_credentials';
  static const next_step = 'next_step';
  static const create_group_failed = 'create_group_failed';
  static const secret_world = 'secret_world';
  static const what_is_mnemonic = 'what_is_mnemonic';
  static const what_is_matatel = 'what_is_matatel';
  static const what_is_identity = 'what_is_identity';
  static const create_account_generate_mnemonic_hint = "create_account_generate_mnemonic_hint";
  static const generate_mnemonic = "generate_mnemonic";
  static const import_mnemonic = 'import_mnemonic';
  static const verify = "verify";
  static const mnemonic_view_hint = "mnemonic_view_hint";
  static const verify_credentials = "verify_credentials";
  static const verify_credentials_hint = "verify_credentials_hint";
  static const please_enter_mnemonic = "please_enter_mnemonic";
  static const nft_advance_number = "nft_advance_number";
  static const address = "address";
  static const not_yet_develop = "not_yet_develop";
  static const please_select_the_mnemonic_in_order_to_ensure_that_the_mnemonic_recorded_in_the_transcription_is_correct =
      "please_select_the_mnemonic_in_order_to_ensure_that_the_mnemonic_recorded_in_the_transcription_is_correct";
  static const welcome_to_the_group_chat = "welcome_to_the_group_chat";
  static const chat_history_cleared_successfully = "chat_history_cleared_successfully";
  static const check_for_updates_failed = "check_for_updates_failed";
  static const message_has_no_content = "message_has_no_content";
  static const unknown_name = "unknown_name";
  static const unknown_account = "unknown_account";
  static const the_message_cannot_be_decrypted_click_to_receive_gain =
      "the_message_cannot_be_decrypted_click_to_receive_gain";
  static const unknown_file = "unknown_file";
  static const image_failed_to_load = "image_failed_to_load";
  static const unknown_message_type = "unknown_message_type";
  static const unknown_alert_message = "unknown_alert_message";
  static const not_a_picture = "not_a_picture";
  static const authentication_failed_please_check_and_try_again = "authentication_failed_please_check_and_try_again";
  static const save_the_mnemonic_successfully = "save_the_mnemonic_successfully";
  static const save_the_mnemonic_failed = "save_the_mnemonic_failed";
  static const selected = "selected";
  static const update_immediately = "update_immediately";
  static const new_version_update = "new_version_update";
  static const update_content = "update_content";
  static const broken_update_package = "broken_update_package";
  static const insufficient_permissions = "insufficient_permissions";
  static const file_download_failed = "file_download_failed";
  static const application_package_parsing_failed = "application_package_parsing_failed";
  static const about_to_install_the_update_package = "about_to_install_the_update_package";
  static const the_mnemonic_is_invalid_please_check_it_carefully_and_try_again =
      "the_mnemonic_is_invalid_please_check_it_carefully_and_try_again";
  static const about = "about";
  static const privacy_policy = "privacy_policy";
  static const terms_of_use = "terms_of_use";
  static const use_comments_or_feedback_suggestions = "use_comments_or_feedback_suggestions";
  static const nick_name = "nick_name";
  static const chat = "chat";
  static const delete_friend = "delete_friend";
  static const view_Avatar = "view_Avatar";
  static const save = "save";
  static const unauthorized = "unauthorized";
  static const group_invalid = "group_invalid";
  static const nickname_has_been_modified_but_not_saved_OK_to_exit =
      "nickname_has_been_modified_but_not_saved_OK_to_exit";
  static const today = "today";
  static const yesterday = "yesterday";
  static const image = "image";
  static const file = "file";
  static const contact = "contact";
  static const the_account_is_illegal_and_forbidden_to_use_pleas_contact_the_administrator =
      "the_account_is_illegal_and_forbidden_to_use_pleas_contact_the_administrator";
  static const my_collection = "my_collection";
  static const collection_list = "collection_list";

  static const collection_avatar = "collection_avatar";
  static const collection_wallpaper = "collection_wallpaper";
  static const set_as_avatar = "set_as_avatar";
  static const avatar_set_successfully = "avatar_set_successfully";
  static const avatar_set_failed = "avatar_set_failed";
  static const set_background = "set_background";
  static const input_max_length = 'input_max_length';
  static const verification_mnemonic = 'verification_mnemonic';
  static const all_member = 'all_member';
  static const account_address_acquisition_failed = 'account_address_acquisition_failed';
  static const all_contacts_chat_background_set_successfully = 'all_contacts_chat_background_set_successfully';
  static const cancel = 'cancel';
  static const mute_talk = 'mute_talk';
  static const admin_rights = 'admin_rights';
  static const year = 'years';
  static const month = 'months';
  static const day = 'days';
  static const am = 'am';
  static const pm = 'pm';
  static const save_and_share = 'save_and_share';
  static const group_management = 'group_management';
  static const personalization_and_others = 'personalization_and_others';
  static const choose_from_the_collection = 'choose_from_the_collection';
  static const channel_share = 'channel_share';
  static const channel_airdrop = "channel_airdrop";
  static const channel_share_generated = "channel_share_generated";
  static const channel_share_generated_txt = "channel_share_generated_txt";
  static const group_admin = "group_admin";
  static const has_joined_this_group = "has_joined_this_group";
  static const this_group_is_invalid = "this_group_is_invalid";
  static const channel_member = "channel_member";
  static const channel_card = 'channel_card';
  static const cannot_join_this_group = 'cannot_join_this_group';
  static const request_sub_channel = 'request_sub_channel';
  static const request_sub_channel_error = 'request_sub_channel_error';
  static const channel_card_save_success = 'channel_card_save_success';
  static const channel_card_save_error = 'channel_card_save_error';
  static const share_info = 'share_info';
  static const join_channel_apply = 'join_channel_apply';
  static const join_channel_apply_text = 'join_channel_apply_text';
  static const all_mute = 'all_mute';
  static const invite_limit = 'invite_limit';
  static const join_group_audit = 'join_group_audit';
  static const admin = 'admin';
  static const confirmed = 'confirmed';
  static const invitation_has_expired = 'invitation_has_expired';
  static const notShare = 'notShare';
  static const customize = 'customize';
  static const mute_time = 'mute_time';
  static const choose_the_duration_of_the_mute = 'choose_the_duration_of_the_mute';
  static const taboo = 'taboo';
  static const taboo_all = 'taboo_all';
  static const other_channel_card = 'other_channel_card';
  static const undo_time = 'undo_time';
  static const select_undo_time = 'select_undo_time';
  static const group_member_mute_body = 'group_member_mute_body';
  static const group_member_remove_mute_body = 'group_member_remove_mute_body';
  static const assigned_as_administrator = 'assigned_as_administrator';
  static const administrator_privileges_revoked = 'administrator_privileges_revoked';
  static const group_kick_member = 'group_kick_member';
  static const open_all_mute = 'open_all_mute';
  static const close_all_mute = 'close_all_mute';
  static const remove_group_member = 'remove_group_member';
  static const confirm_remove_group_member = 'confirm_remove_group_member';
  static const undo_all_msg = 'undo_all_msg';
  static const group_member_details = 'group_member_details';
  static const collection_sticker_messages = 'collection_sticker_messages';
  static const tap_lock_to_stop = 'tap_lock_to_stop';
  static const slide_to_cancel = 'slide_to_cancel';
  static const sticker = 'sticker';
  static const restore_default_background = 'restore_default_background';
  static const click_msg = 'click_msg';
  static const share_to_other = 'share_to_other';
  static const nft_advance_number_tab = 'nft_advance_number_tab';
  static const stare = 'stare';
  static const apply_for_your_nickname_and_avatar = 'apply_for_your_nickname_and_avatar';
  static const refuse = 'refuse';
  static const allow = 'allow';
  static const authorization_failed_please_try_again_late = 'authorization_failed_please_try_again_late';
  static const exceeded_selected = 'exceeded_selected';
  static const fail_to_edit = 'fail_to_edit';
  static const group_only_self = 'group_only_self';
  static const file_select_max_length = 'file_select_max_length';
  static const image_saved_successfully = 'image_saved_successfully';
  static const nft_advance_number_no = 'nft_advance_number_no';
  static const main_chat = 'main_chat';
  static String close_friend = 'close_friend';
  static const theme = 'theme';
  static const wallet = 'wallet';
  static const service_connect_failed = 'service_connect_failed';
  static const channel_group = 'channel_group';
  static const group_chat = 'group_chat';
  static const crowd_creation = 'crowd_creation';
  static const market = 'market';
  static const trend = 'trend';
  static const application = 'application';
  static const set_as_nft_advance_number = 'set_as_nft_advance_number';
  static const nft_advance_number_set_successfully = 'nft_advance_number_set_successfully';
  static const nft_advance_number_not_set = 'nft_advance_number_not_set';
  static const enter_the_authorization_code = 'enter_the_authorization_code';
  static const public_node = 'public_node';
  static const private_node = 'private_node';
  static const node_selection = 'node_selection';
  static const msg_merge_fail = 'msg_merge_fail';
  static const skip = 'skip';
  static const no_relevant_node_data = 'no_relevant_node_data';
  static const sorry_the_current_node_is_not_on_the_chain_please_try_again_later =
      'sorry_the_current_node_is_not_on_the_chain_please_try_again_later';
  static String opinion_feedback_tips = 'opinion_feedback_tips_im';
  static const save_qr = 'save_qr';
  static const save_success = 'save_success';
  static const save_failed = 'save_failed';
  static const authorization_code_exception = 'authorization_code_exception';
  static const authorization_code_exception_1 = 'authorization_code_exception_1';

  static const connect_tips = 'connect_tips';
  static const receive_tips = 'receive_tips';
  static const search_contact_msg = 'search_contact_msg';
  static const more = 'more';
  static const more02 = 'more02';
  static const default_tag = 'default_tag';
  static const set_label = 'set_label';
  static const you_retracted_msg = 'you_retracted_msg';
  static const other_retracted_msg = 'other_retracted_msg';
  static const undo_fail = 'undo_fail';
  static const record_send = 'record_send';
  static const you_can_only_select_up_to_16_images_or_videos = 'you_can_only_select_up_to_16_images_or_videos';
  static const copy_address = 'copy_address';
  static const app_destroyed_about_to_restart = 'app_destroyed_about_to_restart';
  static const app_destroyed_about_to_exit = 'app_destroyed_about_to_exit';
  static const sorry_can_not_add_myself_as_a_friend = 'sorry_can_not_add_myself_as_a_friend';
  static const block_a_close_friend = 'block_a_close_friend';
  static const set_new_avatar = 'set_new_avatar';
  static const set_myself_display_name = 'set_myself_display_name';
  static const set_myself_display_name_hint = 'set_myself_display_name_hint';
  static const press_speak = 'press_speak';
  static const the_group_owner_modified_the_group_information = 'the_group_owner_modified_the_group_information';
  static const remove_group_chat = 'remove_group_chat';
  static const the_message_was_recall = 'the_message_was_recall';
  static const the_length_of_the_silence_is_not_allowed_0 = 'the_length_of_the_silence_is_not_allowed_0';
  static const the_message_cannot_be_empty = 'the_message_cannot_be_empty';
  static const the_message_length_cannot_exceed_1000_characters = 'the_message_length_cannot_exceed_1000_characters';
  static const the_current_conversation_is_exceptional = 'the_current_conversation_is_exceptional';
  static const the_operation_failed = 'the_operation_failed';
  static const log_off_title = 'log_off_title';
  static const log_off_describe = 'log_off_describe';
  static const add_friend_describe = 'add_friend_describe';
  static const delete_account = 'delete_account';
  static const your_account_is_already_logged_in_on_another_device_do_you_want_to_continue =
      'your_account_is_already_logged_in_on_another_device_do_you_want_to_continue';
  static const contact_email = 'contact_email';
  static const email = 'email';
  static const empty = 'empty';
  static const secure_keyboard = 'secure_keyboard';
  static const please_enter_pin = 'please_enter_pin';
  static const set_up_a_pin = 'set_up_a_pin';
  static const modify_pin = 'modify_pin';
  static const pin_set = 'pin_set';
  static const must_be_a_6_digit_password = 'must_be_a_6_digit_password';
  static const pin = 'pin';
  static const hava_collections = 'hava_collections';
  static const new_pin = 'new_pin';
  static const old_pin = 'old_pin';
  static const destroy_describe = 'destroy_describe';
  static const self_destructing_password_and_pin_cannot_be_the_same =
      'self_destructing_password_and_pin_cannot_be_the_same';
  static const pin_and_self_destructing_password_cannot_be_the_same =
      'pin_and_self_destructing_password_cannot_be_the_same';
  static const pin_cannot_be_empty = 'pin_cannot_be_empty';
  static const dao_create_personal = 'dao_create_personal';
  static const dao_create_company = 'dao_create_company';
  static const idcard_not_empty = 'idcard_not_empty';
  static const idcard = 'idcard';
  static const idcard_name = 'idcard_name';
  static const idcard_error = 'idcard_error';
  static const id_name_mismatch = 'id_name_mismatch';
  static const certification_expired = 'certification_expired';
  static const file_transfer_assistant_failed = 'file_transfer_assistant_failed';
  static const file_transfer_assistant_success = 'file_transfer_assistant_success';
  static const trying_register = 'trying_register';
  static const trying_wait = 'trying_wait';
  static const register_network_exception = 'register_network_exception';
  static const verifying_real_name = 'verifying_real_name';
  static const wallet_password_prefix = 'wallet_password_prefix';
  static const set_wallet_password = 'set_wallet_password';
  static const modify_wallet_password = 'modify_wallet_password';
  static const reset_wallet_password = 'reset_wallet_password';
  static const current_password = 'current_password';
  static const new_password = 'new_password';
  static const confirm_password = 'confirm_password';
  static const mnemonic_words_separated_by_spaces = 'mnemonic_words_separated_by_spaces';
  static const the_mnemonic_was_entered_incorrectly_please_make_sure_it_is_the_user_mnemonic =
      'the_mnemonic_was_entered_incorrectly_please_make_sure_it_is_the_user_mnemonic';
  static const set_pwd = 'set_pwd';
  static const balance = 'balance';
  static const payment_amount = 'payment_amount';
  static const max_gas = 'max_gas';
  static const min_gas = 'min_gas';
  static const gas_fee_for_this_transaction = 'gas_fee_for_this_transaction';
  static const please_enter_payment_password = 'please_enter_payment_password';
  static const please_enter_wallet_password = 'please_enter_wallet_password';
  static const fingerprint = 'fingerprint';
  static const face_id = 'face_id';
  static const no_wallet_password_need_set = 'no_wallet_password_need_set';
  static const no_pin_set_need_set = 'no_pin_set_need_set';
  static const no_wallet_password_need_set_to_biometrics = 'no_wallet_password_need_set_to_biometrics';
  static const payment = 'payment';
  static const please_verify_the_payment_with_message = 'please_verify_the_payment_with_message';
  static const authentication_required = 'authentication_required';
  static const authentication_failed_please_try_again = 'authentication_failed_please_try_again';
  static const complaint = "complaint";
  static const complaint_total_msg = "complaint_total_msg";
  static const complaint_message = 'complaint_message';
  static const reason_for_complaint = 'reason_for_complaint';
  static const complaints = 'complaints';
  static const please_input = 'please_input';
  static const submit = 'submit';
  static const please_phone_hint = 'please_phone_hint';
  static const phone_not_empty = 'phone_not_empty';
  static const proxy_settings = 'proxy_settings';
  static const proxy_name_empty = 'proxy_name_empty';
  static const proxy_host_empty = 'proxy_host_empty';
  static const proxy_port_empty = 'proxy_port_empty';
  static const proxy_delete_tips = 'proxy_delete_tips';
  static const add_proxy = 'add_proxy';
  static const proxy_detail = 'proxy_detail';
  static const proxy_name = 'proxy_name';
  static const input_proxy_name = 'input_proxy_name';
  static const proxy_host = 'proxy_host';
  static const input_proxy_host = 'input_proxy_host';
  static const proxy_port = 'proxy_port';
  static const input_proxy_port = 'input_proxy_port';
  static const publish = 'publish';
  static const createDao = 'create_dao';
  static const postNews = 'post_news';
  static const publish_works = 'publish_works';
  static const please_select_content = 'please_select_content';
  static const complaint_success = 'complaint_success';
  static const my_Order = 'my_Order';
  static const create_center = 'create_center';
  static const points_management = 'points_management';
  static const my_account = 'my_account';
  static const no_data = 'no_data';
  static const whether_to_clear_th_cache = 'whether_to_clear_th_cache';
  static const complaints_times = 'complaints_times';
  static const address_not_empty = 'address_not_empty';
  static const code_not_empty = 'code_not_empty';
  static const ddcid_not_empty = 'ddcid_not_empty';
  static const add_collection_success = 'add_collection_success';
  static const add_collection_fail = 'add_collection_fail';
  static const add_collection = 'add_collection';
  static const add_collection_hint = 'add_collection_hint';
  static const input_address_hint = 'input_address_hint';
  static const input_code_hint = 'input_code_hint';
  static const input_ddcid_hint = 'input_ddcid_hint';
  static const ddc = 'ddc';
  static const pledge = 'pledge';
  static const not_sensitive_words = 'not_sensitive_words';
  static const user_agreement_and_privacy_policy = 'user_agreement_and_privacy_policy';
  static const user_agreement_and_privacy_policy_1 = 'user_agreement_and_privacy_policy_1';
  static const user_agreement_and_privacy_policy_title = 'user_agreement_and_privacy_policy_title';
  static const not_agree = 'not_agree';
  static const emoji = 'emoji';
  static const about_ioi = 'about_ioi';
  static const data_backup = 'data_backup';
  static const other_copy_word = 'other_copy_word';
  static const save_to_local = 'save_to_local';
  static const file_assistant_not_online = 'file_assistant_not_online';
  static const input_recover_pwd = 'input_recover_pwd';
  static const backup_recover_hint1 = 'backup_recover_hint1';
  static const backup_recover_hint2 = 'backup_recover_hint2';
  static const data_recover = 'data_recover';
  static const backup_data_enc_fail = 'backup_data_enc_fail';
  static const backup_file_fail = 'backup_file_fail';
  static const backup_file_send = 'backup_file_send';
  static const backup_file_no_exists = 'backup_file_no_exists';
  static const backup_file_dec_fail = 'backup_file_dec_fail';
  static const backup_data_dec_fail = 'backup_data_dec_fail';
  static const not_current_account_backup_data = 'not_current_account_backup_data';
  static const data_backup_success = 'data_backup_success';
  static const no_friend_backup = 'no_friend_backup';
  static const create_account_r = 'create_account_r';
  static const reacquire = 'reacquire';
  static const have_auth_code = 'have_auth_code';
  static const phone_format_war = 'phone_format_war';
  static const data_recover_success = 'data_recover_success';
  static const note_this_operation_only_backs_up_close_friend_data =
      'note_this_operation_only_backs_up_close_friend_data';
  static const no_messages_can_be_forwarded = 'no_messages_can_be_forwarded';
  static const feedback = 'feedback';
  static const help_center = 'help_center';
  static const group_num_max = 'group_num_max';
  static const mnemonic_export = 'mnemonic_export';
  static const register_mnemonic_tips = 'register_mnemonic_tips';
  static const year_of_rabbit_limit = 'year_of_rabbit_limit';
  static const year_of_rabbit_emoji = 'year_of_rabbit_emoji';
  static const db_open_fail = 'db_open_fail';
  static const the_other_party_is_not_your_communication = 'the_other_party_is_not_your_communication';
  static const after_being_added_to_the_blacklist = 'after_being_added_to_the_blacklist';
  static const block = 'block';
  static const unblock = 'un_block';
  static const join_now = 'join_now';
  static const share_promotion = 'share_promotion';
  static const share_poster = 'share_poster';
  static const my_recommender = 'my_recommender';
  static const invited_people = 'invited_people';
  static const please_enter_the_mailing_address_of_your_recommender =
      'please_enter_the_mailing_address_of_your_recommender';
  static const invite_now = 'invite_now';
  static const invitation_list = 'invitation_list';
  static const input_recommender = 'input_recommender';
  static const set_share_invite_text = 'set_share_invite_text';
  static const share_invite_text = 'share_invite_text';
  static const bind_recommender_success = 'bind_recommender_success';
  static const recommender_address_can_not_empty = 'recommender_address_can_not_empty';
  static const invite_friends_to_get_rebates = 'invite_friends_to_get_rebates';
  static const bind_recommender = 'bind_recommender';
  static const registration_time = 'registration_time';
  static const share_pop_text = 'share_pop_text';
  static const my_invite_code = 'my_invite_code';

  static const pornographic_vulgar = 'pornographic_vulgar';
  static const bloody_violence = 'bloody_violence';
  static const false_promotional_links = 'false_promotional_links';
  static const malicious_fraud = 'malicious_fraud';
  static const disgusting_content = 'disgusting_content';
  static const other = 'other';
  static const server_error_retry = 'server_error_retry';
  static const copy_link = 'copy_link';
  static const up_to_people = 'up_to_people';
  static const invitation_sent = 'invitation_sent';
  static const pt_win_3t = 'pt_win_3t';
  static const my_3t = 'my_3t';
  static const view_now = 'view_now';
  static const set = 'set';

  static const chat_mining = 'chat_mining';
  static const detail = 'detail';
  static const total_income = 'total_income';
  static const withdraw = 'withdraw';
  static const invite_new = 'invite_new';
  static const task = 'task';
  static const choose_the_number_of_periods = 'choose_the_number_of_periods';
  static const effective_time_period = 'effective_time_period';
  static const contribution_value_of_the_day = 'contribution_value_of_the_day';
  static const detail_3t = 'detail_3t';
  static const obtained_today = 'obtained_today';
  static const my_3t_value = 'my_3t_value';
  static const detail_3t_list = 'detail_3t_list';
  static const mini_amount = 'mini_amount';
  static const select_mini_amount = 'select_mini_amount';
  static const contribution_value_details = 'contribution_value_details';
  static const completed = 'completed';
  static const undone = 'undone';
  static const total_lower_level_rewards = 'total_lower_level_rewards';
  static const total_activity_rewards = 'total_activity_rewards';
  static const data_overview = 'data_overview';
  static const start_and_end_time = 'start_and_end_time';
  static const the_number_of_participants = 'the_number_of_participants';
  static const mining_points = 'mining_points';
  static const total_ore = 'total_ore';
  static const mining_data_help_1 = 'mining_data_help_1';
  static const mining_data_help_2 = 'mining_data_help_2';
  static const mining_data_help_3 = 'mining_data_help_3';
  static const mining_data_help_4 = 'mining_data_help_4';
  static const mining_data_help_5 = 'mining_data_help_5';
  static const mining_data_help_title_1 = 'mining_data_help_title_1';
  static const mining_data_help_title_2 = 'mining_data_help_title_2';
  static const mining_data_help_title_3 = 'mining_data_help_title_3';
  static const mining_data_help_title_4 = 'mining_data_help_title_4';
  static const mining_data_help_title_5 = 'mining_data_help_title_5';
  static const the_data_shows = 'the_data_shows';
  static const chat_points = 'chat_points';
  static const past_data = 'past_data';
  static const allow_notifications = 'allow_notifications';
  static const turn_on = 'turn_on';
  static const it_is_forbidden_to_run_in_the_emulator_the_program_is_about_to_exit =
      'It_is_forbidden_to_run_in_the_emulator_the_program_is_about_to_exit';
  static const and = 'and';
  static const please_open_permissions = 'please_open_permissions';
  static const view_original_photo = 'view_original_photo';
  static const contact_limit = 'contact_limit';
  static const official_name = 'official_name';
  static const introduce = 'introduce';
  static const official_introduce = 'official_introduce';
  static const card_segment = 'card_segment';
  static const card_segment_hint = 'card_segment_hint';
  static const recharge_amount = 'recharge_amount';
  static const recharge_amount_hint = 'recharge_amount_hint';
  static const unfreeze = 'unfreeze';
  static const transfer = 'transfer';
  static const pending = 'pending';
  static const recharge_record = 'recharge_record';
  static const transaction_hash = 'transaction_hash';
  static const transfer_hint = 'transfer_hint';
  static const unfreeze_hint = 'unfreeze_hint';
  static const bandwidth = 'bandwidth';
  static const energy = 'energy';
  static const ticket = 'ticket';
  static const required_resources = 'required_resources';
  static const handling_fee = 'handling_fee';
  static const reduced_voting_rights = 'reduced_voting_rights';
  static const get_voting_rights = 'get_voting_rights';
  static const the_amount = 'the_amount';
  static const call_contract = 'call_contract';
  static const vote = 'vote';
  static const pledge_2 = 'pledge_2';
  static const execute_the_contract = 'execute_the_contract';
  static const unlock_2 = 'unlock_2';
  static const TRC10_transfer = 'TRC10_transfer';
  static const recharge_amount_must_be_entered = 'recharge_amount_must_be_entered';
  static const you_must_select_an_available_card_segment = 'you_must_select_an_available_card_segment';
  static const first_name_must_be_entered = 'first_name_must_be_entered';
  static const last_name_must_be_entered = 'last_name_must_be_entered';
  static const state_must_be_selected = 'state_must_be_selected';
  static const city_must_be_selected = 'city_must_be_selected';
  static const country_must_be_selected = 'country_must_be_selected';
  static const post_code_must_be_entered = 'post_code_must_be_entered';
  static const address_less_one = 'address_less_one';
  static const open_an_account = 'open_an_account';
  static const open_an_account_failed = 'open_an_account_failed';
  static const no_yet = 'no_yet';
  static const card_ing = 'card_ing';
  static const card_using = 'card_using';
  static const card_freeze = 'card_freeze';
  static const card_del = 'card_del';
  static const card_sys_freeze = 'card_sys_freeze';
  static const card_protect = 'card_protect';
  static const can_not_create_card_max = 'can_not_create_card_max';
  static const amount_is_required = 'amount_is_required';
  static const whether_create_virtual_card_account = 'whether_create_virtual_card_account';
  static const create_virtual_card_account_hint = 'create_virtual_card_account_hint';
  static const has_read_3t_card_user_terms = 'has_read_3t_card_user_terms';
  static const create_card_amount_input_hint = 'create_card_amount_input_hint';
  static const trans_fee_hint = 'trans_fee_hint';
  static const can_create_card_amount = 'can_create_card_amount';
  static const card_opening_amount_must_not_be_less_than_10 = 'card_opening_amount_must_not_be_less_than_10';
  static const trans_the_amount_not_be_less_than = 'trans_the_amount_not_be_less_than';
  static const address_sharing = 'address_sharing';
  static const wallet_address = 'wallet_address';
  static const send_token = 'send_token';
  static const collect_money = 'collect_money';
  static const virtual_open_failed_toast = 'virtual_open_failed_toast';
  static const eth_evm_Address = 'eth_evm_Address';
  static const tron_Address = 'tron_Address';
  static const sign_in_mining = 'sign_in_mining';
  static const sign_in_successfully_contribution_value = 'sign_in_successfully_contribution_value';
  static const sign_in_for = 'sign_in_for';
  static const consecutive_days = 'consecutive_days';
  static const extra_reward_100_contribution_points = 'extra_reward_100_contribution_points';
  static const i_see = 'i_see';
  static const withdraw_hint = 'withdraw_hint';
  static const mining_withdraw_amount = 'mining_withdraw_amount';
  static const please_enter_the_withdrawal_amount = 'please_enter_the_withdrawal_amount';
  static const currently_available = 'currently_available';
  static const has_bean_withdrawn = 'has_bean_withdrawn';
  static const has_sign_in_mining = 'has_sign_in_mining';
  static const points = 'points';
  static const sign_in_today = 'sign_in_today';
  static const mining_withdraw_no_recommender_hint = 'mining_withdraw_no_recommender_hint';
  static const sign_in_failed = 'sign_in_failed';
  static const chat_contribution = 'chat_contribution';
  static const sign_in_reward = 'sign_in_reward';
  static const computing_power_contribution = 'computing_power_contribution';
  static const popular = 'popular';
  static const channel = 'channel';
  static const mining_withdraw_apply_submitted = 'mining_withdraw_apply_submitted';
  static const mining_withdraw_apply_submitted_hint = 'mining_withdraw_apply_submitted_hint';
  static const search_channel = 'search_channel';
  static const introduction_yet = 'introduction_yet';
  static const please_enter_the_withdrawal_address = 'please_enter_the_withdrawal_address';
  static const withdrawal_address = 'withdrawal_address';
  static const browser_download = 'browser_download';
  static const connection_failed = 'connection_failed';
  static const mining_withdraw_same_hint = 'mining_withdraw_same_hint';
  static const mining_withdraw_more_three_hint = 'mining_withdraw_more_three_hint';
  static const insufficient_balance = 'insufficient_balance';
  static const operation_failed_please_try_again = 'operation_failed_please_try_again';
  static const operation_failed_please_contact_admin = 'operation_failed_please_contact_admin';
  static const use_password = 'use_password';
  static const backup_data = 'backup_data';
  static const import_data = 'import_data';
  static const backup_hint_1 = 'backup_hint_1';
  static const backup_hint_2 = 'backup_hint_2';
  static const backup_hint_3 = 'backup_hint_3';
  static const backup_hint_4 = 'backup_hint_4';
  static const data_backup_recover = 'data_backup_recover';
  static const backup_data_generated = 'backup_data_generated';
  static const multi_language = 'multi_language';
  static const follow_the_system = 'follow_the_system';
  static const chinese = 'chinese';
  static const english = 'english';
  static const session_del_toast = 'session_del_toast';
  static const the_computing_power_of_this_period_is_divided_into =
      'the_computing_power_of_this_period_is_divided_into';
  static const direct_subordinate = 'direct_subordinate';
  static const indirect_subordinate = 'indirect_subordinate';
  static const indirect_subordinate2 = 'indirect_subordinate2';
  static const divide_directly_into = 'divide_directly_into';
  static const indirect_share = 'indirect_share';
  static const indirect_share2 = 'indirect_share2';
  static const bonus_type = 'bonus_type';
  static const current_bonus = 'current_bonus';
  static const genesis_node = 'genesis_node';
  static const city_node = 'city_node';
  static const divide_into_detail = 'divide_into_detail';
  static const power_sharing = 'power_sharing';
  static const computational_bonus = 'computational_bonus';
  static const withdrawal_amount_is_at_least = 'withdrawal_amount_is_at_least';
  static const withdrawal_520_3t = 'withdrawal_520_3t';
  static const recommender_msg_body = 'recommender_msg_body';
  static const node_income = 'node_income';
  static const user_name = 'user_name';
  static const pwd = 'pwd';
  static const please_enter_user_name = 'please_enter_user_name';
  static const buy = 'buy';
  static const sell = 'sell';
  static const unit_price = 'unit_price';
  static const total_price = 'total_price';
  static const currency_exchange = 'currency_exchange';
  static const amount = 'amount';
  static const channel_number_count = 'channel_number_count';
  static const approve_number = 'approve_number';
  static const visible_to_all_group_members = 'visible_to_all_group_members';
  static const top_success = 'top_success';
  static const whether_to_display_this_message_at_the_top_of_the_session =
      'whether_to_display_this_message_at_the_top_of_the_session';
  static const top_msg = 'top_msg';
  static const failed_top = 'failed_top';
  static const top_cancel_failed = 'top_cancel_failed';
  static const top_cancel_success = 'top_cancel_success';
  static const send_a_transfer = 'send_a_transfer';
  static const receive_a_transfer = 'receive_a_transfer';
  static const transfer_Notification = 'transfer_Notification';
  static const emoji_from_mit = 'emoji_from_mit';
  static const emoji_down_failed = 'emoji_down_failed';
  static const emoji_decode_failed = 'emoji_decode_failed';
  static const emoji_load_success = 'emoji_load_success';
  static const add_blacklist = 'add_blacklist';
  static const added_blacklist = 'added_blacklist';
  static const removed = 'removed';
  static const remove = 'remove';
  static const bill = 'bill';
  static const personal_information_collection_list = 'personal_information_collection_list';
  static const third_party_information_and_data_sharing = 'third_party_information_and_data_sharing';
  static const image_saved_failed = 'image_saved_failed';
  static const splash_bottom = 'splash_bottom';
  static const free_virtual_memory = 'free_virtual_memory';
  static const import = 'import';
  static const transfer_in = 'transfer_in';
  static const consumption = 'consumption';
  static const trade_detail = 'trade_detail';
  static const transaction_description = 'transaction_description';
  static const pre_authorization_amount = 'pre_authorization_amount';
  static const pre_authorization_currency = 'pre_authorization_currency';
  static const currency = 'currency';
  static const trade_type = 'trade_type';
  static const trade_status = 'trade_status';
  static const third_party_transaction_status = 'third_party_transaction_status';
  static const balance_at_time_of_change = 'balance_at_time_of_change';
  static const logger_type = 'logger_type';
  static const create_time = 'create_time';
  static const trade_id = 'trade_id';
  static const app_more_run = 'app_more_run';
  static const receive_benefits = 'receive_benefits';
  static const receive_benefits_1 = 'receive_benefits_1';
  static const receive_benefits_2 = 'receive_benefits_2';
  static const receive_benefits_3 = 'receive_benefits_3';
  static const get_wallet_address = 'get_wallet_address';
  static const get_user_address = 'get_user_address';
  static const equity_success_info = 'equity_success_info';
  static const computing_power_did = 'computing_power_did';
  static const did_times = 'did_times';
  static const did_number = 'did_number';
  static const did_list = 'did_list';
  static const ai_hosting = 'ai_hosting';
  static const ai_hosting_state = 'ai_hosting_state';
  static const ai_hosting_state_1 = 'ai_hosting_state_1';
  static const ai_hosting_state_2 = 'ai_hosting_state_2';
  static const computing_power_hosting = 'computing_power_hosting';
  static const create_meeting = 'create_meeting';
  static const did_identification = 'did_identification';
  static const equity_success_info_02 = 'equity_success_info_02';
  static const hosting_number = 'hosting_number';
  static const validity_period = 'validity_period';
  static const undownloaded_source_files_cannot_be_shared = 'undownloaded_source_files_cannot_be_shared';
  static const undownloaded = 'undownloaded';
  static const temp_tbs_info = 'temp_tbs_info';
  static const searbar_hint_search_tid = 'searbar_hint_search_tid';
  static const setting_local_proxy = 'setting_local_proxy';
  static const temp_did_identification = 'temp_did_identification';
  static const new_message_count = 'new_message_count';
  static const the_recommender_has_been_bound_please_try_again_the_next_day =
      'the_recommender_has_been_bound_please_try_again_the_next_day';
  static const the_red_envelope = 'the_red_envelope';
  static const the_hongbao_info = 'the_hongbao_info';
  static const the_meeting_info = 'the_meeting_info';

  static const the_hongbao_title = 'the_hongbao_title';
  static const the_nodes_are_inconsistent_please_switch_the_PCnode =
      'the_nodes_are_inconsistent_please_switch_the_PCnode';
  static const storage_optimization = 'storage_optimization';
  static const repurchase = 'repurchase';
  static const apply_for_redemption = 'apply_for_redemption';
  static const can_be_repurchased = 'can_be_repurchased';
  static const repurchase_unit_price = 'repurchase_unit_price';
  static const please_enter_the_repurchase_amount = 'please_enter_the_repurchase_amount';
  static const expected_to_be_available = 'expected_to_be_available';
  static const expected_note = 'expected_note';
  static const exceeds_the_maximum_redeemable_amount = 'exceeds_the_maximum_redeemable_amount';
  static const repurchase_quantity = 'repurchase_quantity';
  static const amount_received = 'amount_received';
  static const state = 'state';
  static const repurchase_time = 'repurchase_time';
  static const repurchase_0 = 'repurchase_0';
  static const repurchase_1 = 'repurchase_1';
  static const repurchase_2 = 'repurchase_2';
  static const repurchase_record = 'repurchase_record';
  static const submit_success = 'submit_success';
  static const repurchase_min_number = 'repurchase_min_number';
  static const repurchase_tid_submit_info = 'repurchase_tid_submit_info';
  static const order_repurchase_tips = 'order_repurchase_tips';
  static const the_server_returned_failure_please_try_again = 'the_server_returned_failure_please_try_again';
  static const operation_restricted = 'operation_restricted';
  static const handling_fee_02 = 'handling_fee_02';
  static const recognize_QR_code = 'recognize_QR_code';
  static const qr_code_info = 'qr_code_info';
  static const protocol_type = 'protocol_type';
  static const http = 'http';
  static const socks5 = 'socks5';
  static const service_connection_failed_please_restart_the_application =
      'service_connection_failed_please_restart_the_application';
  static const please_wait_for_service_update = 'please_wait_for_service_update';
  static const chat_mining_switch = 'chat_mining_switch';
  static const chat_mining_switch_02 = 'chat_mining_switch_02';

  static const human_machine_verification = 'human_machine_verification';
  static const verification_successful = 'verification_successful';
  static const verification_failed = 'verification_failed';
  static const click_close_to_stop_participating_in_chat_mining_rewards =
      'click_close_to_stop_participating_in_chat_mining_rewards';

  static const refresh = 'refresh';
  static const please_set_avatar = 'please_set_avatar';
  static const translate_info = 'translate_info';
  static const translate_desc = 'translate_desc';
  static const node_switching = 'node_switching';
  static const current_node = 'current_node';
  static const available_node = 'available_node';
  static const notice = 'notice';
  static const notice_info = 'notice_info';
  static const confirm_switch = 'confirm_switch';
  static const precautions = 'precautions';
  static const switch_node_precautions = 'switch_node_precautions';
  static const switching_node = 'switching_node';
  static const node_staking_list = 'node_staking_list';
  static const node_staking = 'node_staking';
  static const pledge_application = 'pledge_application';
  static const node_list = 'node_list';
  static const passed = 'passed';
  static const all = 'all';
  static const verifying = 'verifying';
  static const authentication_failed = 'authentication_failed';
  static const node_status = 'node_status';
  static const domain_name = 'domain_name';
  static const type = 'type';
  static const Application_description = 'Application_description';
  static const Application_time = 'Application_time';
  static const k_server = 'k_server';
  static const node_pledge_application = 'node_pledge_application';
  static const wallet_info = 'wallet_info';
  static const master_wallet = 'master_wallet';
  static const service_wallet = 'service_wallet';
  static const service_info = 'service_info';
  static const server_domain_name = 'server_domain_name';
  static const service_type = 'service_type';
  static const Application_description_1 = 'Application_description_1';
  static const submit_your_application = 'submit_your_application';
  static const node_initialization = 'node_initialization';
  static const set_up_the_pledge_pin_code = 'set_up_the_pledge_pin_code';
  static const please_enter_a_new_pin_code = 'please_enter_a_new_pin_code';
  static const the_pin_code_cannot_be_less_than_6_digits = 'the_pin_code_cannot_be_less_than_6_digits';
  static const please_enter_a_new_pin_code_again = 'please_enter_a_new_pin_code_again';
  static const the_pin_codes_entered_twice_are_inconsistent = 'the_pin_codes_entered_twice_are_inconsistent';
  static const please_enter_a_pin_code = 'please_enter_a_pin_code';

  static const the_pin_codes_can_not_empty = 'the_pin_codes_can_not_empty';
  static const the_sevice_wallet_error = 'the_sevice_wallet_error';
  static const the_node_can_not_empty = 'the_node_can_not_empty';
  static const main_wallet_can_not_empty = 'main_wallet_can_not_empty';
  static const server_wallet_can_not_empty = 'server_wallet_can_not_empty';
  static const please_select_the_server_type = 'please_select_the_server_type';
  static const keyBoxType_1 = 'keyBoxType_1';
  static const keyBoxType_2 = 'keyBoxType_2';
  static const keyBoxType_3 = 'keyBoxType_3';
  static const keyBoxType_4 = 'keyBoxType_4';
  static const keyBoxType_5 = 'keyBoxType_5';
  static const keyBoxType_6 = 'keyBoxType_6';
  static const keyBoxType_7 = 'keyBoxType_7';
  static const keyBoxType_8 = 'keyBoxType_8';
  static const keyBoxType_9 = 'keyBoxType_9';
  static const keyBoxType_10 = 'keyBoxType_10';
  static const keyBoxType_11 = 'keyBoxType_11';
  static const keyBoxType_12 = 'keyBoxType_12';

  static const desc_wallet_can_not_empty = 'desc_wallet_can_not_empty';
  static const node_staking_details = 'node_staking_details';
  static const node_pledge_application_info = 'node_pledge_application_info';
  static const edit_pin = 'edit_pin';
  static const activate_keybox = 'activate_keybox';
  static const activate = 'activate';
  static const activated = 'activated';
  static const not_activated = 'not_activated';
  static const old_pin_code = 'old_pin_code';
  static const new_pin_code = 'new_pin_code';
  static const update_pin_code = 'update_pin_code';
  static const the_main_wallet_and_service_wallet_cannot_be_the_same_wallet =
      'the_main_wallet_and_service_wallet_cannot_be_the_same_wallet';
  static const crop = 'crop';
  static const brush = 'brush';
  static const text = 'text';
  static const addtext = 'add_text';
  static const size = 'size';
  static const color = 'color';
  static const initial_node_update = 'initial_node_update';
  static const node_update_success = 'node_update_success';
  static const generating_account = 'generating_account';
  static const un_know = 'un_know';
  static const regular_node = 'regular_node';
  static const block_node = 'block_node';
  static const node_rpc = 'node_rpc';
  static const chain_ID = 'chain_ID';
  static const miner = 'miner';
  static const balance_wallet = 'balance_wallet';
  static const pledge_quantity = 'pledge_quantity';
  static const min_pledge_quantity = 'min_pledge_quantity';
  static const miner_name = 'miner_name';
  static const miner_name_info = 'miner_name_info';
  static const current_status = 'current_status';
  static const current_status_value = 'current_status';
  static const the_pledge_amount_cannot_be_empty = 'the_pledge_amount_cannot_be_empty';
  static const the_pledge_amount_cannot_be_illegal = 'the_pledge_amount_cannot_be_illegal';
  static const the_pledge_amount_cannot_be_less_than_the_minimum_pledge_amount =
      'the_pledge_amount_cannot_be_less_than_the_minimum_pledge_amount';
  static const insufficient_available_balance_please_switch_to_another_wallet =
      'insufficient_available_balance_please_switch_to_another_wallet';
  static const there_is_an_error_in_obtaining_tokens_please_reselect_your_wallet =
      'there_is_an_error_in_obtaining_tokens_please_reselect_your_wallet';
  static const no_corresponding_wallet_address_found_please_import_it_to_the_wallet_first =
      'no_corresponding_wallet_address_found_please_import_it_to_the_wallet_first';

  static const please_update_your_default_nickname = 'please_update_your_default_nickname';
  static const please_restart_app = 'please_restart_app';
  static const activate_wallet = 'activate_wallet';
  static const no_pin_set_activate_wallet_need_set = 'no_pin_set_activate_wallet_need_set';
  static const mnemonic_verify_failed_please_check = 'mnemonic_verify_failed_please_check';
  static const mnemonic_verify_success = 'mnemonic_verify_success';
  static const backup_mnemonic = 'backup_mnemonic';
  static const verify_backup = 'verify_backup';
  static const verify_mnemonic_description = 'verify_mnemonic_description';
  static const close_wallet_confirmation_desc = 'close_wallet_confirmation_desc';
  static const activate_wallet_confirmation_desc = 'activate_wallet_confirmation_desc';
  static const image_save_to_ablum = 'image_save_to_ablum';
  static const image_save_to_ablum_failed = 'image_save_to_ablum_failed';
  static const building_your_digital_fortress = 'building_your_digital_fortress';
  static const comprehensive_protection_for_your_peace_of_mind_online =
      'comprehensive_protection_for_your_peace_of_mind_online';
  static const your_privacy_our_mission = 'your_privacy_our_mission';
  static const take_control_of_your_personal_data_with_linksay = 'take_control_of_your_personal_data_with_linksay';
  static const unleash_digital_freedom_enjoy_boundless_experiences =
      'unleash_digital_freedom_enjoy_boundless_experiences';
  static const breaking_barries_unlocking_possibilities_with_linksay =
      'breaking_barries_unlocking_possibilities_with_linksay';
  static const channel_recommed = 'channel_recommed';
  static const channel_hot = 'channel_hot';
  static const channel_eco = 'channel_eco';
  static const channel_tech = 'channel_tech';
  static const channel_fund = 'channel_fund';
  static const channel_other = 'channel_other';
  static const group_owner_transfer = 'group_owner_transfer';
  static const group_owner_transfer_confirm = 'group_owner_transfer_confirm';
  static const group_owner_transfer_desc = 'group_owner_transfer_desc';
  static const group_owner_transfer_success = 'group_owner_transfer_success';
  static const group_owner_transfer_failed = 'group_owner_transfer_failed';
  static const new_group_owner = 'new_group_owner';

  static const channel_moment_title = 'channel_moment_title';
  static const channel_moment_desc = 'channel_moment_desc';
  static const channel_moment_desc_empty = 'channel_moment_desc_empty';
  static const channel_moment_empty = 'channel_moment_empty';
  static const view_more_comments = 'view_more_comments';
  static const create_post = 'create_post';
  static const see_more = 'see_more';
  static const delete = 'delete';
  static const post = 'post';
  static const create_post_hint = 'create_post_hint';
  static const member = 'member';
  static const maximum_nine_images = 'maximum_nine_images';
  static const upload_success = 'upload_success';
  static const update = 'update';
  static const delete_success = 'delete_success';
  static const delete_post_confirmation_desc = 'delete_post_confirmation_desc';
  static const upload_fail = 'upload_fail';
  static const delete_fail = 'delete_fail';
  static const update_description_dialog_title = 'update_description_dialog_title';
  static const update_description_dialog_hint = 'update_description_dialog_hint';
  static const update_fail = 'update_fail';
  static const update_success = 'update_success';
  static const moment_comment_hint = 'moment_comment_hint';
  static const success = 'success';
  static const failed = 'failed';
  static const delete_comment_confirmation_desc = 'delete_comment_confirmation_desc';
  static const share = 'share';
  static const smart_refresh_no_data = 'smart_refresh_no_data';
  static const smart_refresh_loading = 'smart_refresh_loading';
  static const smart_refresh_can_loading = 'smart_refresh_can_loading';
  static const smart_refresh_idle = 'smart_refresh_idle';
  static const smart_refresh_fail = 'smart_refresh_fail';
  static const exceed_file_size = 'exceed_file_size';
  static const select_image = 'select_image';
  static const select_video = 'select_video';
  static const video_exceed_file_size = 'video_exceed_file_size';
  static const uploading = 'uploading';
  static const processing = 'processing';
  static const video_processing_failed = 'video_processing_failed';
  static const video_upload_failed = 'video_upload_failed';
  static const image_upload_failed = 'image_upload_failed';
  static const video_url = 'video_url';
  static const video_not_support = 'video_not_support';
  static const share_now = 'share_now';
  static const new_update_available = 'new_update_available';
  static const new_update_available_desc = 'new_update_available_desc';
  static const confirm_language = 'confirm_language';
  static const log_off_title_new = 'log_off_title_new';
  static const i_prefer_to_stay = 'i_prefer_to_stay';
  static const log_off_desc_1_1 = 'log_off_desc_1_1';
  static const log_off_desc_1_2 = 'log_off_desc_1_2';
  static const log_off_desc_1_keyword_1 = 'log_off_desc_1_keyword_1';
  static const log_off_desc_2_1 = 'log_off_desc_2_1';
  static const log_off_desc_2_keyword_1 = 'log_off_desc_2_keyword_1';
  static const log_off_desc_3_1 = 'log_off_desc_3_1';
  static const log_off_desc_3_2 = 'log_off_desc_3_2';
  static const log_off_desc_3_keyword_1 = 'log_off_desc_3_keyword_1';
  static const log_off_desc_3_keyword_2 = 'log_off_desc_3_keyword_2';
  static const log_off_desc_4_1 = 'log_off_desc_4_1';
  static const log_off_desc_4_2 = 'log_off_desc_4_2';
  static const log_off_desc_4_keyword_1 = 'log_off_desc_4_keyword_1';
  static const copy_invitation_code = 'copy_invitation_code';
  static const backup_data_title = 'backup_data_title';
  static const backup_data_recovery_password_title = 'backup_data_recovery_password_title';
  static const note_this_operation_only_backs_up_close_friend_data_label =
      'note_this_operation_only_backs_up_close_friend_data_label';
  static const all_terms = 'all_terms';
  static const thank_you_for_choosing_linksay_app = 'thank_you_for_choosing_linksay_app';
  static const export_mnemonic_step_1 = 'export_mnemonic_step_1';
  static const export_mnemonic_step_2 = 'export_mnemonic_step_2';
  static const node_switch_desc_1_1 = 'node_switch_desc_1_1';
  static const node_switch_desc_1_2 = 'node_switch_desc_1_2';
  static const node_switch_desc_1_keyword_1 = 'node_switch_desc_1_keyword_1';
  static const node_switch_desc_2_1 = 'node_switch_desc_2_1';
  static const node_switch_desc_2_2 = 'node_switch_desc_2_2';
  static const node_switch_desc_2_keyword_1 = 'node_switch_desc_2_keyword_1';
  static const node_switch_desc_3_1 = 'node_switch_desc_3_1';
  static const node_switch_desc_3_keyword_1 = 'node_switch_desc_3_keyword_1';
  static const node_switch_desc_4_1 = 'node_switch_desc_4_1';
  static const node_switch_desc_4_keyword_1 = 'node_switch_desc_4_keyword_1';
  static const node_switch_title = 'node_switch_title';
  static const node_switching_title = 'node_switching_title';
  static const node_switching_desc = 'node_switching_desc';
  static const label_address = 'label_address';
  static const label_text = 'label_text';
  static const label_audio = 'label_audio';
  static const label_meeting = 'label_meeting';
  static const clear_now = 'clear_now';
  static const delete_contact_desc = 'delete_contact_desc';
  static const token = 'token';
  static const money_packet = 'money_packet';
  static const money_packet_details = 'money_packet_details';
  static const equal = 'equal';
  static const random = 'random';
  static const amount_per_packet = 'amount_per_packet';
  static const money_packet_type = 'money_packet_type';
  static const max = 'max';
  static const service_fee = 'service_fee';
  static const what_is_money_packet = 'what_is_money_packet';
  static const best_wishes = 'best_wishes';
  static const address_copied = 'address_copied';
  static const total_amount = 'total_amount';
  static const amount_of_packet = 'amount_of_packet';
  static const of_money_packet = 'of_money_packet';
  static const congratulations = 'congratulations';
  static const you_received = 'you_received';
  static const open = 'open';
  static const try_again_later = 'try_again_later';
  static const has_been_snatched = 'has_been_snatched';
  static const total = 'total';
  static const max_number_sent = 'max_number_sent';
  static const token_balance_not_enough = 'token_balance_not_enough';
  static const open_pieces = 'open_pieces';
  static const snatch_red_envelopes = 'snatch_red_envelopes';
  static const meeting = 'meeting';
  static const host = 'host';
  static const upcomings = 'upcomings';
  static const past = 'past';
  static const create_meeting_2_cap = 'create_meeting_2_cap';
  static const upcoming_meeting_empty = 'upcoming_meeting_empty';
  static const past_meeting_empty = 'past_meeting_empty';
  static const date = 'date';
  static const start_time = 'start_time';
  static const end_time = 'end_time';
  static const passcode = 'passcode';
  static const meeting_link = 'meeting_link';
  static const start_meeting = 'start_meeting';
  static const join = 'join';
  static const meeting_detail = 'meeting_detail';
  static const recorded_meeting = 'recorded_meeting';
  static const view = 'view';
  static const here = 'here';
  static const create_new_meeting = 'create_new_meeting';
  static const meeting_title = 'meeting_title';
  static const meeting_description = 'meeting_description';
  static const time_1_cap = 'time_1_cap';
  static const submit_1_cap = 'submit_1_cap';
  static const passcode_desc = 'passcode_desc';
  static const passcode_desc_2 = 'passcode_desc_2';
  static const hint_title = 'hint_title';
  static const hint_description = 'hint_description';
  static const meeting_time_invalid = 'meeting_time_invalid';
  static const meeting_title_empty = 'meeting_title_empty';
  static const meeting_date_required = 'meeting_date_required';
  static const meeting_start_time_required = 'meeting_start_time_required';
  static const meeting_end_time_required = 'meeting_end_time_required';
  static const please_enter_passcode = 'please_enter_passcode';
  static const meeting_passcode_limit = 'meeting_passcode_limit';
  static const start_time_less_than_10_err_msg = 'start_time_less_than_10_err_msg';
  static const any_user_can_open_meeting = 'any_user_can_open_meeting';
  static const any_user_can_open_meeting_2 = 'any_user_can_open_meeting_2';
  static const meeting_host_owner = 'meeting_host_owner';
  static const edit_meeting = 'edit_meeting';
  static const cancel_meeting = 'cancel_meeting';
  static const yesterday_1_cap = 'yesterday_1_cap';
  static const today_1_cap = 'today_1_cap';
  static const tomorrow_1_cap = 'tomorrow_1_cap';
  static const share_meeting_msg_1 = 'share_meeting_msg_1';
  static const share_meeting_msg_2 = 'share_meeting_msg_2';
  static const share_meeting_msg_3 = 'share_meeting_msg_3';
  static const share_meeting_msg_4 = 'share_meeting_msg_4';
  static const share_meeting_msg_5 = 'share_meeting_msg_5';
  static const share_meeting_msg_6 = 'share_meeting_msg_6';
  static const share_meeting_msg_7 = 'share_meeting_msg_7';
  static const any_user_can_open_meeting_indicator = 'any_user_can_open_meeting_indicator';
  static const cancel_meeting_confirmation = 'cancel_meeting_confirmation';
  static const you_have_been_invited_to_join = 'you_have_been_invited_to_join';
  static const click_to_join = 'click_to_join';
  static const generate_meeting_passcode = 'generate_meeting_passcode';
  static const meeting_created_by = 'meeting_created_by';
  static const host_meeting_url = 'host_meeting_url';
  static const host_meeting_url_note = 'host_meeting_url_note';
  static const meeting_date_invalid = 'meeting_date_invalid';
  static const invitation = 'invitation';
  static const mission = 'mission';
  static const download_and_explore_now = 'download_and_explore_now';
  static const label_video_call = 'label_video_call';
  static const btn_invitation_code = 'btn_invitation_code';
  static const btn_download_code = 'btn_download_code';
  static const download = 'download';
  static const user_register_login = 'user_register_login';
  static const phone_number = 'phone_number';
  static const otp_number = 'otp_number';
  static const send_otp = 'send_otp';
  static const game_center_short = 'game_center_short';
  static const session_all = 'session_all';
  static const session_private = 'session_private';
  static const session_channel = 'session_channel';
  static const session_dao = 'session_dao';
  static const session_private_group_chat = 'session_private_group_chat';
  static const search = 'search';
  static const channel_dao = 'channel_dao';
  static const real_name_no_auth = 'real_name_no_auth';
  static const real_name_pass_auth = 'real_name_pass_auth';
  static const game_center_member = 'game_center_member';
  static const game_center = 'game_center';
  static const set_small_name = 'set_small_name';
  static const small_name = 'small_name';
  static const basic_setting = 'basic_setting';
  static const privacy_and_security_setting = 'privacy_and_security_setting';
  static const general = 'general';
  static const pin_status = 'pin_status';
  static const modify = 'modify';
  static const pin_code = 'pin_code';
  static const please_enter_your_pin_code = 'please_enter_your_pin_code';
  static const check_my_recommender = 'check_my_recommender';
  static const invite_more_friend = 'invite_more_friend';
  static const check_invitation_list = 'check_invitation_list';
  static const get_it = 'get_it';
  static const no_referrer_yet_bind_now = 'no_referrer_yet_bind_now';
  static const go_bind = 'go_bind';
  static const bind_referrer_desc = 'bind_referrer_desc';
  static const enter_referrer_invitation_code = 'enter_referrer_invitation_code';
  static const invitation_code = 'invitation_code';
  static const download_qr_code = 'download_qr_code';
  static const invitation_qr_code = 'invitation_qr_code';
  static const already_invited = 'already_invited';
  static const select_from_gallery = 'select_from_gallery';
  static const my_contact = 'my_contact';
  static const join_page_title = 'join_page_title';
  static const group_member_count = 'group_member_count';
  static const join_now_2 = 'join_now_2';
  static const pin_top = 'pin_top';
  static const unpin_top = 'unpin_top';
  static const unread = 'unread';
  static const read = 'read';
  static const meeting_tab_all = 'meeting_tab_all';
  static const all_meeting_empty = 'all_meeting_empty';
  static const meeting_view_detail = 'meeting_view_detail';
  static const meeting_edit = 'meeting_edit';
  static const meeting_share = 'meeting_share';
  static const not_set_password_yet = 'not_set_password_yet';
  static const meeting_date = 'meeting_date';
  static const meeting_time = 'meeting_time';
  static const to = 'to';
  static const meeting_passcode = 'meeting_passcode';
  static const add_image_video = 'add_image_video';
  static const edit_post = 'edit_post';
  static const log_off_desc_5_1 = 'log_off_desc_5_1';
  static const log_off_desc_5_2 = 'log_off_desc_5_2';
  static const log_off_desc_5_keyword_1 = 'log_off_desc_5_keyword_1';
  static const edit_contact_nickname = 'edit_contact_nickname';
  static const edit_contact_nickname_desc = 'edit_contact_nickname_desc';
  static const contact_detail_mute = 'contact_detail_mute';
  static const contact_detail_unmute = 'contact_detail_unmute';
  static const give_admin_rights = 'give_admin_rights';
  static const remove_admin_rights = 'remove_admin_rights';
  static const contact_detail_mute_talk = 'contact_detail_mute_talk';
  static const contact_detail_unmute_talk = 'contact_detail_unmute_talk';
  static const group_detail_share = 'group_detail_share';
  static const invite_to_join = 'invite_to_join';
  static const share_image = "share_image";
  static const share_link = "share_link";
  static const gc_member_m = "gc_member_m";
  static const gc_member_non_m = "gc_member_non_m";
  static const gc_member_expired_date = "gc_member_expired_date";
  static const gc_member_detail = "gc_member_detail";
  static const redeem_now = "redeem_now";
  static const gc_member_privilege_1 = "gc_member_privilege_1";
  static const gc_member_privilege_2 = "gc_member_privilege_2";
  static const gc_member_privilege_3 = "gc_member_privilege_3";
  static const gc_member_privilege_4 = "gc_member_privilege_4";
  static const gc_member_privilege_5 = "gc_member_privilege_5";
}

class Locales {
  static Map<String, String> getLocales(String key) {
    Map<String, String> map = {};
    switch (key) {
      case "en_US":
        map.addAll(LocalesHw.en_US);
        // map.addAll(BL.enUS);
        break;
      case "zh_CN":
        map.addAll(LocalesHw.zh_CN);
        // map.addAll(BL.zhCN);

        break;
    }
    return map;
  }
}
