import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_metatel/app/widgets/button/wood_button.dart';
import 'package:flutter_metatel/app/widgets/shape_border/badge_border.dart';
import 'package:flutter_metatel/core/values/colors.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

import '../../../../core/languages/l.dart';
import '../../../../r.dart';
import '../../../widgets/brown_app_bar.dart';
import '../../../widgets/wooden_long_board.dart';

class MemberView extends GetView {
  const MemberView({super.key});

  @override
  Widget build(BuildContext context) {
    return BrownAppBar(
      title: L.game_center.tr,
      dottedColor: AppColors.colorFFC2AF8C,
      child: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white.withOpacity(0.9),
          image: DecorationImage(image: AssetImage(R.settingBg), fit: BoxFit.cover),
        ),
        child: ScrollConfiguration(
          behavior: ScrollBehavior().copyWith(overscroll: false),
          child: SingleChildScrollView(
            child: SizedBox(
              height: 714.01.r,
              // width: 1.sw,
              child: Stack(
                children: [
                  /// 背景链条左
                  Positioned(
                    top: 0,
                    left: 1.sw / 2 - 115.38.r,
                    child: Image.asset(
                      R.meChain,
                      fit: BoxFit.fill,
                      height: 625.68.r,
                    ),
                  ),

                  /// 背景链条右
                  Positioned(
                    top: 0,
                    right: 1.sw / 2 - 131.27.r,
                    child: Image.asset(
                      R.meChain,
                      fit: BoxFit.fill,
                      height: 625.68.r,
                    ),
                  ),

                  /// 内容
                  Align(
                    alignment: Alignment.topCenter,
                    child: Column(
                      // padding: EdgeInsets.zero,
                      children: [
                        SizedBox(height: 29.53.r),
                        _buildMemberInfo(isMember: false, expiredDate: '2026/05/05'),
                        SizedBox(height: 27.28.r),
                        WoodenLongBoard(
                          text: L.gc_member_detail.tr,
                          width: 326.37.r,
                          padding: EdgeInsets.only(left: 32.12.r),
                        ),
                        SizedBox(height: 12.93.r),
                        _buildPrivilegeBoard(),
                        SizedBox(height: 18.33.r),
                        WoodButton(
                          text: L.redeem_now.tr,
                          buttonWidth: 165.r,
                          onTap: () {},
                        ),
                        SizedBox(height: 20.r),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMemberInfo({bool isMember = false, String? expiredDate}) {
    return SizedBox(
      width: 291.r,
      height: 93.67.r,
      child: Stack(
        children: [
          /// 背景板 2 & role info
          Align(
            alignment: Alignment.centerRight,
            child: Container(
              width: 210.r,
              height: 79.14.r,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(R.gcMemberInfoBg2), fit: BoxFit.fill),
              ),
              child: Padding(
                padding: const EdgeInsets.only(left: 25).r,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isMember ? L.gc_member_m.tr : L.gc_member_non_m.tr,
                      style: TextStyle(
                        fontSize: 19.sp,
                        height: 1.3,
                        fontWeight: FontWeight.w500,
                        color: AppColors.colorFFCAB692,
                      ),
                    ),
                    if (isMember) SizedBox(height: 5.r),
                    if (isMember)
                      Container(
                        padding: EdgeInsets.only(left: 6.25.r, top: 1.5.r, bottom: 1.5.r, right: 8.75.r),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(9.r),
                          color: AppColors.colorFFCAB692,
                        ),
                        child: Text(
                          L.gc_member_expired_date.tr + ': ' + (expiredDate ?? ''),
                          style: TextStyle(
                            fontSize: 10.sp,
                            letterSpacing: 0.6,
                            // height: 1.3,
                            fontWeight: FontWeight.bold,
                            color: AppColors.colorFF251F21,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          /// 背景板 1 & badge
          Align(
            alignment: Alignment.centerLeft,
            child: Container(
              width: 92.03.r,
              height: 93.67.r,
              decoration: BoxDecoration(
                image: DecorationImage(image: AssetImage(R.gcMemberInfoBg1), fit: BoxFit.fill),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.colorFF28231A,
                    blurRadius: 6.r,
                    offset: Offset(4.r, 0),
                  ),
                ],
              ),
              child: Center(
                child: Image.asset(
                  isMember ? R.gcMemberMBadge : R.gcMemberNonMBadge,
                  width: 40.r,
                  height: 46.19.r,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrivilegeBoard() {
    return Container(
      width: 364.49.r,
      height: 408.74.r,
      decoration: BoxDecoration(
        image: DecorationImage(image: AssetImage(R.gcMemberPrivilegeBoardBg), fit: BoxFit.fill),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(height: 15.r),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildPrivilegeButton(text: L.gc_member_privilege_1.tr),
              SizedBox(width: 8.r), // Add spacing between buttons
              _buildPrivilegeButton(text: L.gc_member_privilege_2.tr),
              SizedBox(width: 8.r), // Add spacing between buttons
              _buildPrivilegeButton(text: L.gc_member_privilege_3.tr),
            ],
          ),
          SizedBox(height: 39.r),
          Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              _buildPrivilegeButton(text: L.gc_member_privilege_4.tr),
              SizedBox(width: 8.r), // Add spacing between buttons
              _buildPrivilegeButton(text: L.gc_member_privilege_5.tr),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPrivilegeButton({required String text}) {
    return SizedBox(
      width: 99.8.r,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center, // Center align horizontally
        children: [
          Container(
            width: 78.8.r,
            height: 88.18.r,
            // color: AppColors.bgCreateIdGradientEnd,
            decoration: ShapeDecoration(
              shape: BadgeBorder(),
              color: AppColors.bgCreateIdGradientEnd,
              image: DecorationImage(
                image: AssetImage(R.gcMemberPrivilegeBg),
                fit: BoxFit.fill,
              ),
            ),
          ),
          SizedBox(height: 2.r),
          SizedBox(
            width: 78.r,
            child: Text(
              text,
              maxLines: 2,
              textAlign: TextAlign.center,
              style: TextStyle(
                height: 1.3,
                fontSize: 15.sp,
                fontWeight: FontWeight.bold,
                color: AppColors.colorFFCAB692,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
